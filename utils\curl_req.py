import execjs, json, os, time
from utils.common import guess_content_type
from utils import tk_urlencode_2
from functools import wraps
import requests
from retry import retry
from typing import TYPE_CHECKING, Callable, Protocol, runtime_checkable, Literal
if TYPE_CHECKING:
    from requests import Response

@runtime_checkable
class RetryCapable(Protocol):
    max_fail_count: int

# 死循环重试直到上限
def custom_retry(delay_retry_time=5):
    def decorator(func: Callable[..., dict]):
        @wraps(func)
        def wrapper(self: RetryCapable, *args, **kwargs):
            while True:
                result = func(self, *args, **kwargs)
                if result.get("status"):
                    return result
                self.max_fail_count -= 1
                if self.max_fail_count <= 0:
                    return result
                time.sleep(delay_retry_time)
        return wrapper
    return decorator

class ReqBase(object):
    def __init__(self, config=None, main_path="", cookies_dict={}, proxies=None, redis_url="", max_fail_count=5, ctx_dict={}):
        self.main_path = main_path
        self.session = requests.Session()
        self.cookies_dict = cookies_dict
        self.update_session_cookies(cookies_dict=self.cookies_dict)
        self.proxies = proxies
        # self.proxies = {"http": "http://127.0.0.1:10808", "https": "http://127.0.0.1:10808"}
        self.max_req_timeout = config["max_req_timeout"]
        self.redis_url = redis_url
        self.max_fail_count = max_fail_count
        self.ua: str = config["USER_AGENT"]
        self.ctx_dict = ctx_dict
        self.js_path = self.main_path / "js_path" if self.main_path else ""
        if not self.ctx_dict and self.main_path:
            js_files = os.listdir(self.js_path)
            for js_file in js_files:
                self.ctx_dict["".join(js_file.split(".")[:-1])] = execjs.compile(open(self.js_path / js_file, encoding='utf-8').read())

    # 调用 js 处理消息体的编码
    def use_execjs(self, ctx_key: str="", funcname: str="", params: tuple=()) -> str:
        js_args = ",".join(json.dumps(p) for p in params)
        js_code = f"{funcname}({js_args})"
        encrypt_words = self.ctx_dict[ctx_key].eval(js_code)
        return encrypt_words

    def join_url_params(self, url, params):
        return f'{url}?{tk_urlencode_2(params)}'
    
    def set_session_cookies(self, session: requests.Session, cookies_dict):
        for ck in cookies_dict:
            session.cookies.set(ck, cookies_dict[ck])

    def update_session_cookies(self, cookies_dict={}, session=None):
        assert isinstance(cookies_dict, dict)
        session = session if session else self.session
        self.set_session_cookies(session=session, cookies_dict=cookies_dict)

    @retry(tries=3, delay=2)
    def do_req(self, 
        session: requests.Session=None, 
        method: Literal["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "TRACE", "PATCH"] = "GET", 
        url="", 
        headers=None, 
        data=None, 
        no_proxy=False
    ) -> "Response":
        assert url and method.upper() in {"GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "TRACE", "PATCH"}
        session = session if session else self.session
        proxies = None if no_proxy else self.proxies
        response = session.request(method=method.upper(), url=url, headers=headers, data=data, timeout=self.max_req_timeout, proxies=proxies)
        return response
    
    @retry(tries=3, delay=2)
    def do_request(self, method="GET", url="", headers=None, data=None, no_proxy=False, cookies=None, verify=True, timeout=None):
        assert url and method.upper() in ["GET", "POST"]
        proxies = None if no_proxy else self.proxies
        request_timeout = timeout if timeout is not None else self.max_req_timeout
        if method.upper() == "GET":
            response = requests.get(url, headers=headers, timeout=request_timeout, proxies=proxies, cookies=cookies, verify=verify)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, data=data, timeout=request_timeout, proxies=proxies, cookies=cookies, verify=verify)
        if not response.text or not response.text.strip():
            raise ValueError("服务器返回了空响应")
        return response

    # publish_photourl photo_data photo_size photo_type
    def update_base_photoinfo(self, photo_info):
        get_img_content_result = self.get_img_content(inner_url=photo_info["publish_photourl"])
        if get_img_content_result.get("status"):
            photo_data = get_img_content_result.get("data", {}).get("response_data", b'')
            if not photo_data:
                return get_img_content_result
            photo_size = len(photo_data)
            photo_type = guess_content_type(byte_data=photo_data)
            if not photo_type:
                return {"status": 0, "data": {"text": f'图片 {photo_info["publish_photourl"]} 类型识别失败', "response_data": photo_type}, "login_expired": 0}
            photo_info["photo_data"] = photo_data
            photo_info["photo_size"] = photo_size
            photo_info["photo_type"] = photo_type
            return {"status": 1, "data": {"text": f'图片 {photo_info["publish_photourl"]} 类型识别成功：{photo_type}', "response_data": photo_info}, "login_expired": 0}
        return get_img_content_result

    # publish_videourl video_data video_size video_type
    def update_base_videoinfo(self, video_info):
        # 获取视频内容
        all_file_data = []
        part_byte_start=0
        single_part_size = 2999999 # 一个分段的字节大小
        part_byte_end = single_part_size
        while True:
            if video_info["video_size"] < part_byte_end: # 最后一个分段的大小 = 文件总大小 - 下一分段获取文件字节的起始索引
                part_byte_end = video_info["video_size"] - part_byte_start
            else:
                part_byte_end = part_byte_start + single_part_size
            custom_retry_result = self.get_video_content(inner_url=video_info["publish_videourl"], part_byte_start=part_byte_start, part_byte_end=part_byte_end)
            print(custom_retry_result.get("data", {}).get("text", ""))
            if not custom_retry_result.get("status"):
                return custom_retry_result
            single_part_data = custom_retry_result.get("data", {}).get("response_data", b'')
            all_file_data.append(single_part_data)
            part_byte_start = part_byte_end + 1
            if part_byte_start >= video_info["video_size"]:
                video_data = b''.join(all_file_data)
                if not video_data:
                    return {"status": 0, "data": {"text": f'获取{video_info.get("fill_text")}失败，视频内容为空', "response_data": video_data}, "login_expired": 0}
                video_size = len(video_data) # 字节数
                video_type = guess_content_type(byte_data=video_data)
                if not video_type:
                    return {"status": 0, "data": {"text": f'图片 {video_info["publish_videourl"]} 类型识别失败', "response_data": video_type}, "login_expired": 0}
                video_info["video_data"] = video_data
                video_info["video_size"] = video_size
                video_info["video_type"] = video_type
                break
        return {"status": 1, "data": {"text": f'视频 {video_info["publish_videourl"]} 基础信息成功', "response_data": video_info}, "login_expired": 0}

    # 获取内部图片资源内容
    @custom_retry(delay_retry_time=5)
    def get_img_content(self, inner_url=""):
        try:
            with requests.Session() as session:
                img_response = session.get(inner_url)
                img_response_content = img_response.content
            if isinstance(img_response_content, bytes) and (img_response_content not in [
                b'<!doctype html>\n<html lang=en>\n<title>404 Not Found</title>\n<h1>Not Found</h1>\n<p>The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.</p>\n',
                b'{"detail":"Not Found"}'
            ]):
                return {"status": 1, "data": {"text": f"获取图片 {inner_url} 成功", "response_data": img_response_content}, "login_expired": 0}
            return {"status": 0, "data": {"text": f"获取图片 {inner_url} 失败", "response_data": img_response_content}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f"获取图片 {inner_url} 失败：{e}", "response_data": ""}, "login_expired": 0}
       
    # 获取内部视频资源内容（分段获取）
    @custom_retry(delay_retry_time=5)
    def get_video_content(self, inner_url="", part_byte_start=0, part_byte_end=2999999):
        try:
            with requests.Session() as session:
                video_response = session.get(inner_url, headers={"Range": f"bytes={part_byte_start}-{part_byte_end}"}, timeout=self.max_req_timeout)
                video_response_content = video_response.content
            if isinstance(video_response_content, bytes) and video_response_content not in [
                b'{"data":null,"msg":"\xe6\x96\x87\xe4\xbb\xb6\xe4\xb8\x8d\xe5\xad\x98\xe5\x9c\xa8","re_code":-1,"success":false}\n'
            ]:
                return {"status": 1, "data": {"text": f"获取视频 {inner_url} 分段 {part_byte_start}~{part_byte_end} 成功", "response_data": video_response_content}, "login_expired": 0}
            return {"status": 0, "data": {"text": f"获取视频 {inner_url} 分段 {part_byte_start}~{part_byte_end} 失败", "response_data": video_response_content}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f"获取视频 {inner_url} 分段 {part_byte_start}~{part_byte_end} 失败：{e}", "response_data": ""}, "login_expired": 0}