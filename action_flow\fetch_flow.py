import logging
import time
from typing import Callable

from action_base import FetchBase

logger = logging.getLogger(__name__)


def _execute_callback_with_retry(callback: Callable, data: list, retries: int, delay: int):
    if not callback or not data:
        return {"status": 1}
    for attempt in range(retries):
        try:
            callback(data)
            return {"status": 1}
        except Exception as e:
            logger.error(f"回调执行失败 (尝试 {attempt + 1}/{retries}): {e}", exc_info=True)
            if attempt < retries - 1:
                time.sleep(delay)
                continue
            else:
                return {"status": 0, "data": {"text": f"回调函数在 {retries} 次尝试后失败: {e}"}}
    return {"status": 0, "data": {"text": "未知的回调执行错误"}}


def _paginated_list_fetch_flow(fetch_method: Callable, identifier: str, callback: Callable, list_key: str,
                               id_key: str, **kwargs):
    """
    通用的分页列表采集流程。
    """
    task_type = kwargs.get("task_type", "unknown_task")
    task_id = kwargs.get("task_id")
    small_task_id = kwargs.get("small_task_id")
    logger = logging.getLogger(task_type)
    log_context = f"[[{task_type}:{task_id}:{small_task_id}:{identifier}]]"

    batch_items = []
    pages_in_batch = 0
    total_items_processed = 0

    cursor = kwargs.pop("cursor", None)
    count = kwargs.pop("count", None)
    batch_size = kwargs.pop("batch_size", 300)
    callback_retries = kwargs.pop("callback_retries", 3)
    callback_retry_delay = kwargs.pop("callback_retry_delay", 1)

    internal_keys_to_remove = ['task_type', 'task_id', 'small_task_id', 'callback', 'list_key', 'id_key']
    for key in internal_keys_to_remove:
        kwargs.pop(key, None)

    logger.debug(f"{log_context} 开始分页采集, 批次大小: {batch_size}")

    while True:
        try:
            fetch_params = kwargs.copy()
            fetch_params[id_key] = identifier
            if cursor is not None:
                fetch_params['cursor'] = cursor
            if count is not None:
                fetch_params['count'] = count

            result = fetch_method(**fetch_params)
        except Exception as e:
            cursor_val = cursor if cursor not in (None, "") else 0
            error_msg = f"采集异常: {e.__class__.__name__}"
            logger.error(f"{log_context} {error_msg}")
            if batch_items:
                logger.warning(f"{log_context} 异常发生前，回调已缓存的 {len(batch_items)} 个条目")
                _execute_callback_with_retry(callback, batch_items, callback_retries, callback_retry_delay)
            return {"status": 0, "data": {"text": error_msg, "cursor": cursor_val}}

        if not result.get("status"):
            error_text = result.get('data', {}).get('text', '底层采集方法返回失败')
            cursor_val = cursor if cursor not in (None, "") else 0
            logger.error(f"{log_context} 采集失败: {error_text}")
            if batch_items:
                logger.warning(f"{log_context} 失败前，回调已缓存的 {len(batch_items)} 个条目")
                _execute_callback_with_retry(callback, batch_items, callback_retries, callback_retry_delay)
            return {"status": 0, "data": {"text": "采集失败", "cursor": cursor_val}}

        if result.get("not_exist"):
            logger.info(f"{log_context} 目标不存在或私密，采集结束")
            if batch_items:
                _execute_callback_with_retry(callback, batch_items, callback_retries, callback_retry_delay)
            return {"status": 1, "data": {"text": "账号不存在或内容私密"}, "not_exist": True}

        result_data = result.get("data", {})
        items = result_data.get(list_key, [])
        if items:
            total_items_processed += len(items)
            batch_items.extend(items)
            pages_in_batch += 1
            logger.debug(f"{log_context} 本页获得 {len(items)} 个条目, 缓存中共有 {len(batch_items)} 个")

        if len(batch_items) >= batch_size:
            logger.info(f"{log_context} 达到批次大小，回调 {len(batch_items)} 个条目")
            callback_result = _execute_callback_with_retry(callback, batch_items, callback_retries,
                                                           callback_retry_delay)
            if not callback_result.get("status"):
                return callback_result
            batch_items = []
            pages_in_batch = 0

        cursor = result_data.get("cursor") or result_data.get("maxCursor")
        has_more = result_data.get("hasMore") if result_data.get("hasMore") is not None else result_data.get("has_more")

        if not has_more:
            logger.info(f"{log_context} 已无更多数据")
            break

    if batch_items:
        logger.info(f"{log_context} 处理最后一批 {len(batch_items)} 个条目")
        _execute_callback_with_retry(callback, batch_items, callback_retries, callback_retry_delay)

    logger.info(f"{log_context} 分页采集完成，总计处理 {total_items_processed} 个条目")
    return {"status": 1, "data": {"text": "采集完成"}}


class FetchFlow(FetchBase):

    def get_user_info_flow(self, user_identifier: str, **kwargs):
        """获取用户信息的流程"""
        log_context = f"[{kwargs.get('task_type', 'user_info')}:{user_identifier}]"
        logger.debug(f"{log_context} 开始获取用户信息")
        try:
            result = self._get_user_info(unique_id=user_identifier)

            if result.get("status") == 1:
                final_result = {"status": 1, "data": {"response_data": result.get("data")}}
                if result.get("not_exist"):
                    final_result["not_exist"] = True

                # 对 blogger_collect 任务的特殊处理，它也需要回调
                if kwargs.get("callback"):
                    response_data = final_result.get("data", {}).get("response_data")
                    if response_data:
                        # 此处回调的应该是原始的包含 status 的 result
                        _execute_callback_with_retry(kwargs["callback"], [result])

                return final_result
            else:
                return {"status": 0, "data": {"text": result.get("error", "获取用户信息失败")}}
        except Exception as e:
            # 捕获所有异常，返回错误状态
            logger.error(f"{log_context} 获取用户信息时发生异常: {e}")
            return {"status": 0, "data": {"text": f"获取用户信息失败: {str(e)}"}}

    def get_video_info_flow(self, video_id: str, **kwargs):
        """采集视频详情, 并确保返回统一的状态格式。"""
        try:
            result = self.get_video_by_id(video_id=video_id)

            if result.get("status") == 1:
                final_result = {"status": 1, "data": {"response_data": result.get("data")}}
                if result.get("not_exist"):
                    final_result["not_exist"] = True
            else:
                final_result = {"status": 0, "data": {"text": result.get("error", "获取视频信息失败")}}

            if kwargs.get("callback") and final_result.get("status") == 1:
                callback_result = _execute_callback_with_retry(kwargs["callback"], [result])
                if not callback_result.get("status"):
                    final_result["status"] = 0
                    final_result["data"]["text"] = f"采集成功，但回调失败: {callback_result.get('data', {}).get('text')}"

            return final_result
        except Exception as e:
            logger.error(f"获取视频详情时发生未知异常: {e}", exc_info=True)
            return {"status": 0, "data": {"text": f"获取视频详情异常: {e}"}}

    def get_user_videos_flow(self, user_identifier: str, callback: Callable, **kwargs):
        """分页采集用户视频列表, 按批次通过回调处理. 支持 sec_uid 任务级缓存。"""
        sec_uid = kwargs.get("sec_uid")
        if not sec_uid:
            try:
                sec_uid = self._resolve_sec_uid(user_identifier, task_type=kwargs.get("task_type"))
                if not sec_uid:
                    # 只有_resolve_sec_uid返回None时才是用户不存在
                    return {"status": 1, "data": {"text": f"用户 {user_identifier} 不存在"}, "not_exist": True}
            except Exception as e:
                # 错误，返回失败状态而不是not_exist
                return {"status": 0, "data": {"text": f"解析用户信息失败: {str(e)}"}}
        # 本流程后续都用本地 sec_uid 变量
        result = _paginated_list_fetch_flow(
            fetch_method=self.get_user_videos,
            identifier=sec_uid,
            callback=callback,
            list_key="itemList",
            id_key="user_identifier",
            **kwargs
        )
        if sec_uid:
            result["sec_uid"] = sec_uid
        return result

    def _get_social_list_flow(self, fetch_method: Callable, user_identifier: str, callback: Callable, **kwargs):
        """分页采集用户关注/粉丝列表的通用逻辑. sec_uid 任务级缓存。"""
        sec_uid = kwargs.get("sec_uid")
        if not sec_uid:
            try:
                sec_uid = self._resolve_sec_uid(user_identifier, task_type=kwargs.get("task_type"))
                if not sec_uid:
                    # 只有_resolve_sec_uid返回None时才是用户不存在
                    return {"status": 1, "data": {"text": f"用户 {user_identifier} 不存在"}, "not_exist": True}
            except Exception as e:
                # 错误，返回失败状态而不是not_exist
                return {"status": 0, "data": {"text": f"解析用户信息失败: {str(e)}"}}

        # 将解析出的sec_uid放入kwargs，供底层函数使用
        kwargs['sec_uid'] = sec_uid

        # 底层函数已统一，不再需要包装
        result = _paginated_list_fetch_flow(
            fetch_method=fetch_method,
            identifier=sec_uid,
            callback=callback,
            list_key="userList",
            id_key="sec_uid",  # 底层用的是sec_uid
            **kwargs
        )
        if sec_uid:
            result["sec_uid"] = sec_uid
        return result

    def get_follow_list_flow(self, user_identifier: str, callback: Callable, **kwargs):
        """分页采集用户关注列表, 按批次通过回调处理. 支持 unique_id 和 sec_uid."""
        return self._get_social_list_flow(
            fetch_method=self.get_follow_list,
            user_identifier=user_identifier,
            callback=callback,
            **kwargs
        )

    def get_follower_list_flow(self, user_identifier: str, callback: Callable, **kwargs):
        """分页采集用户粉丝列表, 按批次通过回调处理. 支持 unique_id 和 sec_uid."""
        return self._get_social_list_flow(
            fetch_method=self.get_follower_list,
            user_identifier=user_identifier,
            callback=callback,
            **kwargs
        )

    def get_video_comments_flow(self, video_id: str, callback: Callable, **kwargs):
        """分页采集视频评论列表, 按批ce通过回调处理."""
        return _paginated_list_fetch_flow(
            fetch_method=self.get_video_comments,
            identifier=video_id,
            callback=callback,
            list_key="commentList",
            id_key="video_id",
            **kwargs
        )

    def get_comment_replies_flow(self, comment_id: str, item_id: str, callback: Callable, **kwargs):
        """分页采集评论回复列表, 按批次通过回调处理. (独立实现分页)"""
        task_type = kwargs.get("task_type", "comment_reply_collect")
        task_id = kwargs.get("task_id")
        small_task_id = kwargs.get("small_task_id")
        logger = logging.getLogger(task_type)
        identifier = f"{item_id}/{comment_id}"
        log_context = f"[[{task_type}:{task_id}:{small_task_id}:{identifier}]]"

        batch_items = []
        total_items_processed = 0

        batch_size = kwargs.pop("batch_size", 300)
        callback_retries = kwargs.pop("callback_retries", 3)
        callback_retry_delay = kwargs.pop("callback_retry_delay", 1)

        current_cursor = kwargs.pop("cursor", None)
        count = kwargs.pop("count", None)

        logger.debug(f"{log_context} 开始分页采集, 批次大小: {batch_size}")

        while True:
            try:
                api_params = {
                    "comment_id": comment_id,
                    "item_id": item_id,
                    "cursor": current_cursor,
                }
                if count is not None:
                    api_params['count'] = count

                result = self.get_comment_replies(**api_params)
            except Exception as e:
                cursor_val = current_cursor if current_cursor is not None else 0
                error_msg = f"采集异常: {e.__class__.__name__}"
                logger.error(f"{log_context} {error_msg}")
                if batch_items:
                    logger.warning(f"{log_context} 异常发生前，回调已缓存的 {len(batch_items)} 个条目")
                    _execute_callback_with_retry(callback, batch_items, callback_retries, callback_retry_delay)
                return {"status": 0, "data": {"text": error_msg, "cursor": cursor_val}}

            if not result.get("status"):
                error_text = result.get('data', {}).get('text', '底层采集方法返回失败')
                cursor_val = current_cursor if current_cursor is not None else 0
                logger.error(f"{log_context} 采集失败: {error_text}")
                if batch_items:
                    logger.warning(f"{log_context} 失败前，回调已缓存的 {len(batch_items)} 个条目")
                    _execute_callback_with_retry(callback, batch_items, callback_retries, callback_retry_delay)
                return {"status": 0, "data": {"text": "采集失败", "cursor": cursor_val}}

            if result.get("not_exist"):
                logger.info(f"{log_context} 目标不存在或私密，采集结束")
                if batch_items:
                    _execute_callback_with_retry(callback, batch_items, callback_retries, callback_retry_delay)
                return {"status": 1, "data": {"text": "评论不存在或回复已关闭"}, "not_exist": True}

            result_data = result.get("data", {})
            items = result_data.get("commentList", [])
            if items:
                total_items_processed += len(items)
                batch_items.extend(items)
                logger.debug(f"{log_context} 本页获得 {len(items)} 个条目, 缓存中共有 {len(batch_items)} 个")

            if len(batch_items) >= batch_size:
                logger.info(f"{log_context} 达到批次大小，回调 {len(batch_items)} 个条目")
                callback_result = _execute_callback_with_retry(callback, batch_items, callback_retries,
                                                               callback_retry_delay)
                if not callback_result.get("status"):
                    return callback_result
                batch_items = []

            current_cursor = result_data.get("cursor") or result_data.get("maxCursor")
            has_more = result_data.get("hasMore") if result_data.get("hasMore") is not None else result_data.get(
                "has_more")

            if not has_more:
                logger.info(f"{log_context} 已无更多数据")
                break

        if batch_items:
            logger.info(f"{log_context} 处理最后一批 {len(batch_items)} 个条目")
            _execute_callback_with_retry(callback, batch_items, callback_retries, callback_retry_delay)

        logger.info(f"{log_context} 分页采集完成，总计处理 {total_items_processed} 个条目")
        return {"status": 1, "data": {"text": "采集完成"}}

    def _resolve_sec_uid(self, user_identifier, **kwargs):
        """解析用户sec_uid, 支持传入unique_id或sec_uid"""
        if user_identifier.startswith("7"):  # 简单判断是否是sec_uid
            return user_identifier

        user_info_result = self.get_user_info_flow(user_identifier, **kwargs)

        # 只有明确标记为不存在时才返回None
        if user_info_result.get("not_exist"):
            return None

        # status=0的情况抛出异常，而不是返回None
        if not user_info_result.get("status"):
            error_text = user_info_result.get("data", {}).get("text", "获取用户信息失败")
            raise Exception(f"获取用户信息失败: {error_text}")

        # 正常解析sec_uid
        try:
            sec_uid = user_info_result["data"]["response_data"]["userInfo"]["user"]["secUid"]
            return sec_uid if sec_uid else None
        except (KeyError, TypeError) as e:
            raise Exception(f"数据结构解析错误: {e}")
