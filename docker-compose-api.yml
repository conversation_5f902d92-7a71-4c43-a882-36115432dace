version: '3.8'

services:
  tiktok-api:
    build:
      context: .
      dockerfile: Dockerfile-api
    container_name: tiktok_api_service
    ports:
      - "8000:8000"
    volumes:
      - ./config.json:/app/config.json:ro
    environment:
      - RELOAD=false
      - TZ=Asia/Shanghai
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - tiktok-api-network

networks:
  tiktok-api-network:
    driver: bridge