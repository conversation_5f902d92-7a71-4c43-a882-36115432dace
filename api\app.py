
import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Generator

from fastapi import FastAPI, Query, HTTPException, Depends, Security, APIRouter
from fastapi.security import APIKeyHeader
from fastapi.middleware.cors import CORSMiddleware
from starlette import status
from starlette.requests import Request
from starlette.responses import JSONResponse

from api.config import get_settings, Settings
from api.tiktok_api import TikTokFetcher
from utils.msTokenUtil import get_tiktok_token
from utils.errors import ServiceError, UserNotFoundError, VideoNotFoundError, CommentNotFoundError


class AppState:
    def __init__(self):
        self.settings = get_settings()
        self.shared_data: Dict[str, str] = {"ms_token": self.settings.default_ms_token}

        cookies_dict = self._parse_cookies(self.settings.cookies)
        self.fetcher = TikTokFetcher(
            settings=self.settings,
            shared_data=self.shared_data,
            cookies_dict=cookies_dict
        )
        self.token_update_task: asyncio.Task = None

    def _parse_cookies(self, cookies_str: str) -> dict:
        cookies_dict = {}
        if not cookies_str:
            return cookies_dict

        for cookie in cookies_str.split(';'):
            cookie = cookie.strip()
            if not cookie:
                continue
            if '=' in cookie:
                key, value = cookie.split('=', 1)
                cookies_dict[key.strip()] = value.strip()
        return cookies_dict

    async def update_token(self):
        try:
            current_token = self.shared_data.get("ms_token")
            token_info = await get_tiktok_token(
                config=self.settings.model_dump(),
                token=current_token,
                proxy=self.settings.proxy_url
            )
            if token_info and token_info.get("msToken"):
                new_token = token_info["msToken"]
                if new_token != current_token:
                    self.shared_data["ms_token"] = new_token
                    print(f"Token updated: {new_token[:30]}...")
        except Exception as e:
            print(f"Token update failed: {e}")

    async def periodic_token_updater(self):
        await self.update_token()
        while True:
            await asyncio.sleep(self.settings.token_update_interval_hours * 3600)
            await self.update_token()

    async def startup(self):
        print("Starting app...")
        self.token_update_task = asyncio.create_task(self.periodic_token_updater())

    async def shutdown(self):
        if self.token_update_task:
            self.token_update_task.cancel()
            try:
                await self.token_update_task
            except asyncio.CancelledError:
                pass
        await self.fetcher.session.aclose()


app_state = AppState()


def get_fetcher() -> Generator[TikTokFetcher, None, None]:
    yield app_state.fetcher


api_key_header_scheme = APIKeyHeader(name="X-API-KEY", auto_error=False)


async def get_api_key(api_key_header: str = Security(api_key_header_scheme)):
    settings = app_state.settings

    if not settings.api_secret_key:
        return

    if not api_key_header:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API Key is missing",
            headers={"WWW-Authenticate": "Header"},
        )

    if api_key_header not in settings.api_secret_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API Key",
            headers={"WWW-Authenticate": "Header"},
        )


@asynccontextmanager
async def lifespan(app: FastAPI):
    await app_state.startup()
    yield
    await app_state.shutdown()


app = FastAPI(
    title="TikTok Web API",
    description="TikTok Web API 服务",
    version="1.0.0",
    lifespan=lifespan,
)

api_router = APIRouter(
    dependencies=[Depends(get_api_key)]
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def create_error_response(status_code: int, message: str, not_exist: bool = False) -> JSONResponse:
    return JSONResponse(
        status_code=status_code,
        content={"status": 0, "message": message, "not_exist": not_exist, "data": {}}
    )


@app.exception_handler(HTTPException)
async def http_exception_handler(_: Request, exc: HTTPException):
    headers = exc.headers if exc.headers else {}
    if exc.status_code in [401, 403] and "WWW-Authenticate" not in headers:
        headers["WWW-Authenticate"] = "Bearer"
    response = create_error_response(exc.status_code, exc.detail)
    response.headers.update(headers)
    return response


@app.exception_handler(UserNotFoundError)
async def user_not_found_exception_handler(_: Request, exc: UserNotFoundError):
    return create_error_response(404, f"User not found: {exc}", not_exist=True)


@app.exception_handler(VideoNotFoundError)
async def video_not_found_exception_handler(_: Request, exc: VideoNotFoundError):
    return create_error_response(404, f"Video not found: {exc}", not_exist=True)


@app.exception_handler(CommentNotFoundError)
async def comment_not_found_exception_handler(_: Request, exc: CommentNotFoundError):
    return create_error_response(404, f"Comment not found or disabled: {exc}", not_exist=True)


@app.exception_handler(ServiceError)
async def service_exception_handler(_: Request, exc: ServiceError):
    return create_error_response(400, f"Service error: {exc}")


@app.exception_handler(Exception)
async def generic_exception_handler(request: Request, exc: Exception):
    return create_error_response(500, f"An unexpected internal error occurred: {type(exc).__name__}")


def format_success_response(data, message="Success", not_exist=False):
    return {"status": 1, "message": message, "not_exist": not_exist, "data": data}


async def _resolve_sec_uid(user_identifier: str, fetcher: TikTokFetcher = Depends(get_fetcher)) -> str:
    if user_identifier.startswith("7") and len(user_identifier) > 60:
        return user_identifier

    user_info = await fetcher.get_user_info(unique_id=user_identifier)
    try:
        sec_uid = user_info["user"]["secUid"]
        if not sec_uid:
            raise KeyError
        return sec_uid
    except (KeyError, TypeError):
        raise HTTPException(status_code=404, detail=f"无法从用户信息中为 '{user_identifier}' 解析sec_uid。")


@app.get("/health", summary="健康检查", tags=["系统"], include_in_schema=False)
async def health_check():
    return {"status": "healthy", "service": "TikTok API"}


@api_router.get("/user/info", summary="获取用户详情", tags=["用户"])
async def get_user_info(
        user_identifier: str = Query(..., description="用户唯一ID (unique_id) 或 sec_uid"),
        fetcher: TikTokFetcher = Depends(get_fetcher)
):
    if len(user_identifier) > 40:
        data = await fetcher.get_user_info(sec_uid=user_identifier)
    else:
        data = await fetcher.get_user_info(unique_id=user_identifier)
    return format_success_response(data)


@api_router.get("/user/videos", summary="获取用户的视频列表", tags=["用户"])
async def get_user_videos(
        sec_uid: str = Depends(_resolve_sec_uid),
        cursor: int = Query(0, description="分页游标"),
        count: int = Query(35, description="每页数量 (1-35)", ge=1, le=35),
        fetcher: TikTokFetcher = Depends(get_fetcher)
):
    data = await fetcher.get_user_videos(sec_uid=sec_uid, cursor=cursor, count=count)
    return format_success_response(data)


@api_router.get("/user/following", summary="获取用户的关注列表", tags=["用户"])
async def get_user_following(
        sec_uid: str = Depends(_resolve_sec_uid),
        cursor: int = Query(0, description="分页游标"),
        count: int = Query(30, description="每页数量 (1-30)", ge=1, le=30),
        fetcher: TikTokFetcher = Depends(get_fetcher)
):
    data = await fetcher.get_follow_list(sec_uid=sec_uid, cursor=cursor, count=count)
    return format_success_response(data)


@api_router.get("/user/followers", summary="获取用户的粉丝列表", tags=["用户"])
async def get_user_followers(
        sec_uid: str = Depends(_resolve_sec_uid),
        cursor: int = Query(0, description="分页游标"),
        count: int = Query(30, description="每页数量 (1-30)", ge=1, le=30),
        fetcher: TikTokFetcher = Depends(get_fetcher)
):
    data = await fetcher.get_follower_list(sec_uid=sec_uid, cursor=cursor, count=count)
    return format_success_response(data)


@api_router.get("/video/info", summary="获取视频详情", tags=["视频"])
async def get_video_info(
        video_id: str = Query(..., description="视频ID"),
        fetcher: TikTokFetcher = Depends(get_fetcher)
):
    data = await fetcher.get_video_by_id(video_id=video_id)
    return format_success_response(data)


@api_router.get("/video/comments", summary="获取视频评论", tags=["视频"])
async def get_video_comments(
        video_id: str = Query(..., description="视频ID"),
        cursor: int = Query(0, description="分页游标"),
        count: int = Query(50, description="每页数量 (1-50)", ge=1, le=50),
        fetcher: TikTokFetcher = Depends(get_fetcher)
):
    data = await fetcher.get_video_comments(video_id=video_id, cursor=cursor, count=count)
    return format_success_response(data)


@api_router.get("/comment/replies", summary="获取评论的回复列表", tags=["评论"])
async def get_comment_replies(
        comment_id: str = Query(..., description="评论ID"),
        item_id: str = Query(..., description="视频ID (item_id)"),
        cursor: int = Query(0, description="分页游標"),
        count: int = Query(50, description="每页数量 (1-50)", ge=1, le=50),
        fetcher: TikTokFetcher = Depends(get_fetcher)
):
    data = await fetcher.get_comment_replies(comment_id=comment_id, item_id=item_id, cursor=cursor, count=count)
    return format_success_response(data)


app.include_router(api_router)
