import time
from action_base import ModifyBase
from utils import guess_content_type

class ModifyFlow(ModifyBase):
    # 更改个人用户名（唯一 id），30天一改
    def change_unique_id_flow(self, new_unique_id):
        if not self.session.cookies.get_dict().get("tt_csrf_token"):
            get_index_page_result = self.get_index_page()
            if not get_index_page_result.get("status"):
                return get_index_page_result
        
        check_unique_id_result = self.check_unique_id(new_unique_id=new_unique_id)
        print(check_unique_id_result)
        if not check_unique_id_result.get("status"):
            return check_unique_id_result

        time.sleep(3)
        change_unique_id_result = self.change_unique_id(new_unique_id)
        if not change_unique_id_result.get("status"):
            return change_unique_id_result
        
        # 更新当前类中存储的个人信息
        self.unique_id = new_unique_id
        get_index_page_result = self.get_index_page()
        if not get_index_page_result.get("status"):
            return get_index_page_result
        return change_unique_id_result
    
    # 修改个人头像流程
    def change_profile_pic(self, profile_img_url=""):
        try:
            if profile_img_url:
                self.profile_img_url = profile_img_url
            if not self.profile_img_url:
                return {"status": 0, "data": {"text": f"{self.unique_id} 未提供需要上传的头像路径", "response_data": ""}, "login_expired": 0}
            
            # 重新清空原有内容，确保初始化
            self.profile_data = b'' # 头像的字节数据
            self.mime_type = "" # 头像的图片类型
            
            # 获取头像内容
            while True:
                get_profile_pic_content_result = self.get_img_content(inner_url=self.profile_img_url)
                if get_profile_pic_content_result.get("status"):
                    break
                self.max_fail_count -= 1
                if self.max_fail_count <= 0:
                    return get_profile_pic_content_result
            
            # 识别图片类型
            self.profile_data = get_profile_pic_content_result.get("data", {}).get("response_data", b'')
            self.mime_type = guess_content_type(self.profile_data)
            if not self.mime_type:
                return {"status": 0, "data": {"text": f"{self.unique_id} 识别头像 {self.profile_img_url} 类型失败", "response_data": ""}, "login_expired": 0}

            upload_profile_pic_result = self.upload_profile_pic()
            if not upload_profile_pic_result.get("status"):
                return upload_profile_pic_result
            
            if not self.session.cookies.get_dict().get("tt_csrf_token"):
                get_index_page_result = self.get_index_page()
                if not get_index_page_result.get("status"):
                    return get_index_page_result
            
            time.sleep(3)
            change_info_result = self.change_info(avatar_uri=upload_profile_pic_result.get("data", {}).get("response_data", ""))
            return change_info_result
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 修改个人头像主流程出错：{e}", "response_data": ""}, "login_expired": 0}
        
    # 修改个人昵称/签名流程
    def change_nicknames_signatures(self, new_nickname="", signature_text=""):
        try:
            if (not new_nickname) and (not signature_text):
                return {"status": 0, "data": {"text": f"{self.unique_id} 修改个人昵称/签名未提供相关信息", "response_data": {"new_nickname": new_nickname, "signature_text": signature_text}}, "login_expired": 0}

            time.sleep(3)
            return self.change_info(
                new_nickname=new_nickname,
                signature_text=signature_text
            )
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 修改个人昵称/签名主流程出错：{e}", "response_data": ""}, "login_expired": 0}
        
    # 设置个人账户私密/公开
    def set_private_account_flow(self, setting_type=1):
        try:
            if setting_type not in [0, 1]:
                return {"status": 0, "data": {"text": f'{self.unique_id} 设置个人是否私密账号类型错误 {setting_type}', "response_data": setting_type}, "login_expired": 0}
        
            return self.set_private_account(setting_type=setting_type)
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 设置个人是否私密账号主流程出错：{e}", "response_data": ""}, "login_expired": 0}