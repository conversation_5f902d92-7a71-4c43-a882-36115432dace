import re

# 正则表达式列表，匹配所有需要清理的日志模式
patterns = [
    r".*?任务接口响应：\{'data': \[], 'msg': '成功', 're_code': 0, 'success': True\}.*",
    r".*\[心跳\] 活跃进程: \d+.*"  # 匹配心跳日志
]

# 日志文件路径
logfilepath = r'D:\code\codeProject\tiktok_pro\tiktok_spider\tk_project.log'

# 读取日志文件
try:
    with open(logfilepath, 'r', encoding='utf-8') as file:
        lines = file.readlines()
except FileNotFoundError:
    print(f"错误: 日志文件未找到 '{logfilepath}'")
    exit()

# 找出要删除和要保留的行
lines_to_delete = []
lines_to_keep = []
for line in lines:
    matched = False
    for pattern in patterns:
        # 使用 re.search, 因为它可以在字符串的任何位置进行匹配
        if re.search(pattern, line):
            matched = True
            break
    if matched:
        lines_to_delete.append(line)
    else:
        lines_to_keep.append(line)

# 输出将要删除的行（用于调试）
if lines_to_delete:
    print("将要删除的行：")
    for line_to_delete in lines_to_delete:
        print(line_to_delete.strip())
else:
    print("没有匹配到需要删除的行。")


# 将结果写回文件
with open(logfilepath, 'w', encoding='utf-8') as file:
    file.writelines(lines_to_keep)

# 输出删除统计
print(f"\n完成删除操作。共删除了 {len(lines_to_delete)} 行。")
