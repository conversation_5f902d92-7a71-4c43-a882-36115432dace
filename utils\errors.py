"""统一错误处理"""
from typing import Optional, Dict, Any


class ServiceError(Exception):
    """服务错误基类"""

    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        """初始化"""
        self.message = message
        self.error_code = error_code or "UNKNOWN_ERROR"
        self.details = details or {}
        super().__init__(message)

    def to_dict(self) -> Dict[str, Any]:
        """转为字典"""
        return {
            "success": False,
            "error": self.message,
            "error_code": self.error_code,
            "details": self.details
        }


class UserNotFoundError(ServiceError):
    """当用户不存在或私密时引发"""
    pass


class VideoNotFoundError(ServiceError):
    """当视频不存在、被删除或私密时引发"""
    pass


class CommentNotFoundError(ServiceError):
    """当评论区关闭或评论不存在时引发"""
    pass


class NetworkError(ServiceError):
    """网络错误"""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, "NETWORK_ERROR", details)


class APIError(ServiceError):
    """API错误"""

    def __init__(self, message: str, status_code: int = 0, details: Optional[Dict[str, Any]] = None):
        error_details = details or {}
        error_details["status_code"] = status_code
        super().__init__(message, f"API_ERROR_{status_code}", error_details)


class ResourceNotFoundError(ServiceError):
    """资源不存在"""

    def __init__(self, resource_type: str, resource_id: str, details: Optional[Dict[str, Any]] = None):
        message = f"{resource_type} 不存在: {resource_id}"
        error_details = details or {}
        error_details.update({
            "resource_type": resource_type,
            "resource_id": resource_id
        })
        super().__init__(message, "RESOURCE_NOT_FOUND", error_details)


class ValidationError(ServiceError):
    """验证错误"""

    def __init__(self, message: str, field: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        error_details = details or {}
        if field:
            error_details["field"] = field
        super().__init__(message, "VALIDATION_ERROR", error_details)


class AuthenticationError(ServiceError):
    """认证错误"""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, "AUTHENTICATION_ERROR", details)


class RateLimitError(ServiceError):
    """速率限制"""

    def __init__(self, message: str, retry_after: Optional[int] = None, details: Optional[Dict[str, Any]] = None):
        error_details = details or {}
        if retry_after:
            error_details["retry_after"] = retry_after
        super().__init__(message, "RATE_LIMIT_ERROR", error_details)


class DatabaseError(ServiceError):
    """数据库错误"""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, "DATABASE_ERROR", details)


class ProxyError(ServiceError):
    """代理错误"""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, "PROXY_ERROR", details)


def wrap_exception(e: Exception) -> ServiceError:
    """包装异常为ServiceError"""
    if isinstance(e, ServiceError):
        return e

    # 根据异常类型选择错误类
    error_type = type(e).__name__
    message = str(e)

    if "timeout" in error_type.lower() or "timeout" in message.lower():
        return NetworkError(f"请求超时: {message}")
    elif "connect" in error_type.lower() or "connection" in message.lower():
        return NetworkError(f"连接错误: {message}")
    elif "not found" in message.lower() or "404" in message:
        return ResourceNotFoundError("资源", message)
    elif "validation" in error_type.lower() or "invalid" in message.lower():
        return ValidationError(message)
    elif "auth" in error_type.lower() or "unauthorized" in message.lower() or "403" in message:
        return AuthenticationError(message)
    elif "rate" in error_type.lower() or "limit" in message.lower() or "429" in message:
        return RateLimitError(message)
    elif "database" in error_type.lower() or "db" in error_type.lower() or "sql" in error_type.lower():
        return DatabaseError(message)
    elif "proxy" in error_type.lower() or "proxy" in message.lower():
        return ProxyError(message)
    else:
        return ServiceError(f"未预期的错误: {message}")
