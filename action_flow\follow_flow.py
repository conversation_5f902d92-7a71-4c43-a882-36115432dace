from action_base import FollowBase
from utils import fix_mentions, parse_single_post_feature_info_text

class FollowFlow(FollowBase):
    def comment_video_flow(self, author_unique_id="", video_id="", comment_text="", user_id_map={}, cid="", reply_to_reply_id=""):
        try:
            if not video_id:
                return {"status": 0, "data": {"text": f'{self.unique_id} 评论视频未提供 video_id', "response_data": video_id}, "login_expired": 0}
                
            if not comment_text:
                return {"status": 0, "data": {"text": f'{self.unique_id} 评论视频未提供 comment_text', "response_data": comment_text}, "login_expired": 0}

            comment_text = fix_mentions(comment_text)
            parse_result = parse_single_post_feature_info_text(text=comment_text, user_id_map=user_id_map)
            if parse_result.get("text_extra"):
                mid_text_extra = parse_result["text_extra"]
                text_extra = []
                for it in mid_text_extra:
                    text_extra.append({"user_id": it["user_id"],"type":0, "start": it["start"], "end": it["end"]})
            else:
                text_extra = []
            return self.comment_video(author_unique_id=author_unique_id, video_id=video_id, comment_text=comment_text, text_extra=text_extra, cid=cid, reply_to_reply_id=reply_to_reply_id)
        except Exception as e:
            return {"status": 0, "data": {"text": f'{self.unique_id} 评论视频主流程出错：{e}', "response_data": ""}, "login_expired": 0}