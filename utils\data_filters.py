"""
数据过滤工具模块
提供各种数据过滤函数，用于处理API返回的数据
"""
import time
from typing import Dict, Any, Optional


def filter_video_item(item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """过滤视频条目"""
    if not item:
        return None

    collect_time = int(time.time())

    # 获取或设置默认值
    author = item.get("author", {})
    stats = item.get("stats", {})
    video = item.get("video", {})

    # 构建过滤后的数据结构
    filtered_item = {
        "author": {
            "avatarLarger": author.get("avatarLarger", ""),
            # "avatarMedium": author.get("avatarMedium", ""),
            # "avatarThumb": author.get("avatarThumb", ""),
            "id": author.get("id", ""),
            "nickname": author.get("nickname", ""),
            "uniqueId": author.get("uniqueId", "")
        },
        "createTime": item.get("createTime", 0),
        "collectTime": collect_time,  # 使用当前时间作为采集时间
        "desc": item.get("desc", ""),
        "id": item.get("id", ""),
        "awemeId": item.get("id", ""),  # 兼容旧字段
        "stats": {
            "collectCount": stats.get("collectCount", 0),
            "commentCount": stats.get("commentCount", 0),
            "diggCount": stats.get("diggCount", 0),
            "playCount": stats.get("playCount", 0),
            "shareCount": stats.get("shareCount", 0)
        },
        "video": {
            "cover": video.get("cover", ""),
            "duration": video.get("duration", 0),
            # "dynamicCover": video.get("dynamicCover", ""),
            # "playAddr": video.get("playAddr", "")
        }
    }

    return filtered_item


def filter_social_user(item: Dict[str, Any]) -> Dict[str, Any]:
    """过滤社交用户"""
    if not item:
        return {}

    user_info = item.get("user", item)
    if not user_info or "uniqueId" not in user_info:
        return {}  # 无效的用户对象

    stats = item.get("stats", {})
    collect_time = int(time.time())

    return {
        "id": user_info.get("id"),
        "uniqueId": user_info.get("uniqueId"),
        "nickname": user_info.get("nickname"),
        "avatarThumb": user_info.get("avatarThumb"),
        "signature": user_info.get("signature", ""),
        "secUid": user_info.get("secUid"),
        "privateAccount": user_info.get("privateAccount", False),
        "verified": user_info.get("verified", False),
        "relation": user_info.get("relation"),
        "stats": {
            "followerCount": stats.get("followerCount", 0),
            "followingCount": stats.get("followingCount", 0),
            "heartCount": stats.get("heartCount", 0),
            "videoCount": stats.get("videoCount", 0),
        },
        "collectTime": collect_time
    }


def filter_comment_item(comment: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """过滤社交用户"""
    if not comment:
        return None

    user = comment.get("user", {})
    avatar_thumb = user.get("avatar_thumb", {})

    filtered_comment = {
        "cid": comment.get("cid"),
        "aweme_id": comment.get("aweme_id"),
        "comment_language": comment.get("comment_language"),
        "create_time": comment.get("create_time"),
        "digg_count": comment.get("digg_count"),
        "reply_comment_total": comment.get("reply_comment_total"),
        "text": comment.get("text"),
        "collect_time": str(int(time.time())),  # 当前时间作为采集时间
        "user": {
            "avatar_thumb": {
                "url_list": avatar_thumb.get("url_list", [])
            },
            "nickname": user.get("nickname")
        }
    }

    return filtered_comment


def filter_single_video_data(raw_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    过滤单个视频信息。
    """
    try:
        item_struct = raw_data.get('itemInfo', {}).get("itemStruct", {})
        if not item_struct:
            return {}

        video_info = item_struct.get('video', {})
        author_info = item_struct.get('author', {})
        author_stats = item_struct.get('authorStats', {})  # 获取作者统计信息
        stats_info = item_struct.get("stats", {})
        now_time = int(time.time())

        # 确保所有统计字段都存在
        stats_fields = ["collectCount", "commentCount", "diggCount", "playCount", "shareCount"]
        for field in stats_fields:
            if field not in stats_info:
                stats_info[field] = 0

        # 将作者信息和统计信息合并，以便传递给 filter_social_user
        author_combined = author_info.copy()
        author_combined['stats'] = author_stats

        return {
            "id": item_struct.get("id", ""),
            "awemeId": item_struct.get("id", ""),
            "desc": item_struct.get("desc", ""),
            "createTime": item_struct.get("createTime", 0),
            "collectTime": now_time,
            "video": {
                "cover": video_info.get("cover", ""),
                "duration": video_info.get("duration", 0),
            },
            "stats": stats_info,
            "author": filter_social_user(author_combined)
        }
    except Exception:
        return {}


def filter_user_profile(data: Dict[str, Any], source: str = "api") -> Dict[str, Any]:
    """过滤来自用户详情页或API的用户信息"""
    if not data:
        return {}

    user_info = data.get("userInfo", data)
    user = user_info.get("user", {})
    stats = user_info.get("stats", {})

    # 将用户信息和统计信息合并，以便传递给 filter_social_user
    user_combined = user.copy()
    user_combined['stats'] = stats

    filtered_profile = {
        "user": filter_social_user(user_combined),
        "stats": {
            "followerCount": stats.get("followerCount", 0),
            "followingCount": stats.get("followingCount", 0),
            "heartCount": stats.get("heart", stats.get("heartCount", 0)),  # 兼容不同版本
            "videoCount": stats.get("videoCount", 0),
            "diggCount": stats.get("diggCount", 0),
            "friendCount": stats.get("friendCount", 0),
        },
        "itemList": [filter_video_item(item) for item in user_info.get("itemList", []) if item],
        "source": source
    }
    return filtered_profile


def filter_video_list(data: Dict[str, Any]) -> Dict[str, Any]:
    """过滤视频列表响应"""
    return {
        "itemList": [filter_video_item(item) for item in data.get("itemList", []) if item],
        "cursor": str(data.get("cursor", "")),
        "hasMore": data.get("hasMore", False),
        "statusCode": data.get("status_code", data.get("statusCode", 0)),
        "success": True
    }


def filter_user_list(data: Dict[str, Any]) -> Dict[str, Any]:
    """过滤用户列表（关注/粉丝）响应"""
    return {
        "userList": [filter_social_user(item) for item in data.get("userList", []) if item],
        "cursor": str(data.get("cursor", data.get("minCursor", ""))),
        "hasMore": data.get("hasMore", False),
        "statusCode": data.get("status_code", data.get("statusCode", 0)),
        "success": True
    }


def filter_comment_list(data: Dict[str, Any]) -> Dict[str, Any]:
    """过滤评论列表响应"""
    comments = data.get("comments") or []
    return {
        "commentList": [filter_comment_item(item) for item in comments if item],
        "cursor": str(data.get("cursor", "")),
        "hasMore": data.get("has_more", False),
        "statusCode": data.get("status_code", data.get("statusCode", 0)),
        "success": True
    }


def remove_none_values(data):
    """
    递归地从字典和列表中移除值为None、空字符串、空列表或空字典的键。
    保留值为 0 或 False 的键。
    """
    if isinstance(data, dict):
        cleaned_dict = {k: remove_none_values(v) for k, v in data.items()}
        return {k: v for k, v in cleaned_dict.items() if v or v == 0 or v is False}
    elif isinstance(data, list):
        cleaned_list = [remove_none_values(item) for item in data]
        return [item for item in cleaned_list if item or item == 0 or item is False]
    else:
        return data
