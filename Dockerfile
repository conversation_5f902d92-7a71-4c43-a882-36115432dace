FROM python:3.12-slim-bullseye AS builder

ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

RUN --mount=type=cache,target=/var/cache/apt,sharing=locked \
    --mount=type=cache,target=/var/lib/apt,sharing=locked \
    apt-get update && apt-get install -y libmagic1

RUN python -m venv /opt/venv

COPY requirements.txt .
RUN --mount=type=cache,target=/root/.cache/pip \
    . /opt/venv/bin/activate && \
    pip install --no-cache-dir --upgrade pip -r requirements.txt


FROM python:3.12-slim-bullseye

ENV TZ=Asia/Shanghai

RUN --mount=type=cache,target=/var/cache/apt,sharing=locked \
    --mount=type=cache,target=/var/lib/apt,sharing=locked \
    apt-get update && apt-get install -y --no-install-recommends libmagic1 tzdata nodejs

COPY --from=builder /opt/venv /opt/venv

WORKDIR /app

ENV PATH="/opt/venv/bin:$PATH"

COPY action_base/ ./action_base/
COPY action_flow/ ./action_flow/
COPY manager_base/ ./manager_base/
COPY utils/ ./utils/
COPY js_path/ ./js_path/
COPY manager.py .
COPY export_interface.py .

RUN mkdir -p /app/logs

CMD ["python", "manager.py"] 