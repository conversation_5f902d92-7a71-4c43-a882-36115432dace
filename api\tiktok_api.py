import re
import json
import time
from typing import Dict, Any

import httpx
from tenacity import retry, stop_after_attempt, wait_fixed

from api.config import Settings
from utils.data_filters import (
    filter_user_profile, filter_comment_list, filter_single_video_data,
    remove_none_values, filter_video_list, filter_user_list
)
from utils.errors import ServiceError, UserNotFoundError, VideoNotFoundError, CommentNotFoundError
from utils.tk_algorithm import get_XBogus, get_signature, tk_urlencode_2


class TikTokFetcher:

    def __init__(self, settings: Settings, shared_data: dict, cookies_dict: dict = None):
        self.settings = settings
        self.shared_data = shared_data
        self.ua = self.settings.user_agent
        self.browser_version = "/".join(self.ua.split("/")[1:]) if self.ua else ""

        self.session = httpx.AsyncClient(
            proxy=self.settings.proxy_url,
            timeout=self.settings.max_req_timeout,
            cookies=cookies_dict or {},
            verify=False
        )

    def join_url_params(self, url, params):
        """拼接URL参数"""
        return f'{url}?{tk_urlencode_2(params)}'

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
    async def do_request(
        self,
        method: str = "GET",
        url: str = "",
        headers: dict = None,
        data: dict = None,
        cookies: dict = None,
        timeout: int = None,
        content: bytes = None
    ) -> httpx.Response:
        assert url and method.upper() in ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "TRACE", "PATCH"]

        request_timeout = timeout if timeout is not None else self.settings.max_req_timeout

        # 确保至少有一个 user-agent 头
        final_headers = {"User-Agent": self.ua}
        if headers:
            final_headers.update(headers)

        response = await self.session.request(
            method=method.upper(),
            url=url,
            headers=final_headers,
            data=data,
            content=content,
            cookies=cookies,
            timeout=request_timeout,
        )

        if not response.text or not response.text.strip():
            raise ValueError("服务器返回了空响应")

        response.raise_for_status()  # 检查HTTP错误状态
        return response



    async def _get_user_info_from_webpage(self, username: str) -> Dict[str, Any]:
        url = f"https://www.tiktok.com/@{username}"
        headers = {'Accept': '*/*', 'Host': 'www.tiktok.com', 'Connection': 'keep-alive'}
        try:
            response = await self.do_request("GET", url, headers=headers)
            pattern = r'"webapp\.user-detail":({.*?}),"webapp'
            match = re.search(pattern, response.text)
            if not match:
                raise UserNotFoundError(f"在网页中未找到用户 '{username}' 的信息。")

            user_data = json.loads(match.group(1))
            if 'userInfo' not in user_data:
                raise ServiceError("网页数据中缺少 'userInfo' 字段。")

            original_user_info = user_data.get('userInfo', {})
            filtered_user_info = filter_user_profile(original_user_info, source="web_page")

            raw_user = original_user_info.get("user", {})
            if raw_user:
                if "region" in raw_user:
                    filtered_user_info["user"]["region"] = raw_user["region"]
                if "language" in raw_user:
                    filtered_user_info["user"]["language"] = raw_user["language"]
            
            return filtered_user_info

        except httpx.RequestError as e:
            raise ServiceError(f"获取用户 '{username}' 网页信息时网络错误: {e}") from e
        except Exception as e:
            raise ServiceError(f"解析用户 '{username}' 网页信息时发生异常: {e}") from e

    async def get_user_info(self, sec_uid: str = None, unique_id: str = None, prefer_api: bool = False) -> Dict[str, Any]:
        if sec_uid and unique_id:
            unique_id = None
        if unique_id and unique_id.isdigit():
            raise UserNotFoundError(f"用户ID '{unique_id}' 格式无效。")

        if unique_id and not prefer_api:
            try:
                return await self._get_user_info_from_webpage(unique_id)
            except (UserNotFoundError, ServiceError) as e:
                print(f"网页解析失败，将回退到API: {e}")

        url = "https://www.tiktok.com/api/user/detail/"
        now_time = int(time.time())
        params = {
            "WebIdLastTime": now_time, "aid": "1988", "app_language": "en", "app_name": "tiktok_web",
            "browser_language": "zh-SG", "browser_name": "Mozilla", "browser_online": "true",
            "browser_platform": "Win32", "browser_version": self.browser_version, "channel": "tiktok_web",
            "cookie_enabled": "true", "device_id": self.settings.default_device_id,
            "device_platform": "web_pc",
            "focus_state": "true", "from_page": "user", "history_len": "4", "is_fullscreen": "false",
            "is_page_visible": "true", "language": "en", "os": "windows", "priority_region": "CN", "referer": "",
            "region": "JP", "screen_height": "864", "screen_width": "1536",
            "uniqueId": unique_id or "", "secUid": sec_uid or "",
            "webcast_language": "en", "msToken": self.shared_data.get("ms_token"),
            "coverFormat": "2",
            "needPinnedItemIds": "true", "post_item_list_request_type": "0", "count": "2", "cursor": "0"
        }
        params["X-Bogus"] = get_XBogus(params=params, ua=self.ua, now_time=now_time)
        params["_signature"] = get_signature(X_Bogus=params["X-Bogus"], ua=self.ua)
        request_url = self.join_url_params(url, params)

        try:
            response = await self.do_request("GET", request_url, cookies={"tt-target-idc": "useast8"})
            result = response.json()
            
            if result.get("statusCode") in [10221, 10202, 100002]:
                raise UserNotFoundError(f"用户不存在或私密 (API code: {result.get('statusCode')})")
            
            if result.get("statusCode") != 0:
                 raise ServiceError(f"获取用户信息API返回错误: {result.get('statusMsg', result.get('statusCode'))}")
            
            if not result.get("userInfo"):
                raise ServiceError("获取用户信息成功，但内容为空。")
                
            return filter_user_profile(result, source="api")

        except httpx.HTTPStatusError as e:
            if e.response.status_code == 400 and e.response.json().get("statusCode") in [10221, 10202]:
                raise UserNotFoundError(f"用户不存在或私密 (HTTP 400 with API code: {e.response.json().get('statusCode')})")
            raise ServiceError(f"获取用户信息时发生HTTP错误: {e}") from e
        except Exception as e:
            # 捕获其他如JSON解码等潜在错误
            raise ServiceError(f"处理用户信息API响应时发生异常: {e}") from e

    async def get_video_by_id(self, video_id: str) -> Dict[str, Any]:
        url = "https://www.tiktok.com/api/item/detail/"
        now_time = int(time.time())
        params = {
            "WebIdLastTime": str(now_time), "aid": "1988", "app_language": "en", "app_name": "tiktok_web",
            "browser_language": "en-US", "browser_name": "Mozilla", "browser_online": "true",
            "browser_platform": "Win32", "browser_version": self.browser_version, "channel": "tiktok_web",
            "cookie_enabled": "true", "device_id": self.settings.default_device_id,
            "device_platform": "web_pc",
            "focus_state": "true", "from_page": "user", "history_len": "4", "is_fullscreen": "false",
            "is_page_visible": "true", "language": "en", "os": "windows", "priority_region": "US",
            "referer": "", "region": "US", "root_referer": "https://www.tiktok.com/",
            "screen_height": "1080", "screen_width": "1920", "webcast_language": "en",
            "itemId": video_id
        }
        params["X-Bogus"] = get_XBogus(params=params, data="", ua=self.ua, now_time=now_time, canvas_num=self.settings.canvas_num)
        params["_signature"] = get_signature(url=url, params=params, X_Bogus=params["X-Bogus"], data="", ua=self.ua, now_time=now_time, canvas_num=self.settings.canvas_num)
        request_url = self.join_url_params(url, params)

        try:
            response = await self.do_request("GET", request_url)
            result = response.json()
            if result.get("statusCode") in [10204, 10201, 10221, 10223, 10225]:
                raise VideoNotFoundError(f"视频不存在或已被删除 (API code: {result.get('statusCode')})")
            if result.get("statusCode") != 0:
                raise ServiceError(result.get('status_msg') or f"获取视频详情失败 (API code: {result.get('statusCode')})")
            
            return filter_single_video_data(result)
            
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                raise VideoNotFoundError(f"视频不存在 (HTTP 404)")
            raise ServiceError(f"获取视频详情时发生HTTP错误: {e}") from e
        except Exception as e:
            raise ServiceError(f"处理视频详情API响应时发生异常: {e}") from e

    async def get_user_videos(self, sec_uid: str, cursor: int = 0, count: int = 35) -> Dict[str, Any]:
        url = "https://www.tiktok.com/api/post/item_list/"
        now_time = int(time.time())
        params = {
            "aid": "1988", "app_language": "en", "count": str(count), "cursor": str(cursor), "secUid": sec_uid,
            "WebIdLastTime": now_time, "app_name": "tiktok_web", "browser_language": "zh-SG",
            "browser_name": "Mozilla", "browser_online": "true", "browser_platform": "Win32",
            "browser_version": self.browser_version, "channel": "tiktok_web", "cookie_enabled": "true",
            "device_id": self.settings.default_device_id, "device_platform": "web_pc", "focus_state": "true",
            "from_page": "user", "history_len": "4", "is_fullscreen": "false", "is_page_visible": "true",
            "language": "en", "os": "windows", "priority_region": "CN", "referer": "", "region": "JP",
            "screen_height": "864", "screen_width": "1536", "tz_name": "Asia/Shanghai",
            "user_is_login": "true", "webcast_language": "en", "msToken": self.shared_data.get("ms_token")
        }
        params["X-Bogus"] = get_XBogus(params=params, ua=self.ua, now_time=now_time)
        params["_signature"] = get_signature(X_Bogus=params["X-Bogus"], ua=self.ua)
        request_url = self.join_url_params(url, params)

        try:
            response = await self.do_request("GET", request_url, cookies={"tt-target-idc": "useast8"})
            result = response.json()
            if result.get("statusCode") == 10221:
                raise UserNotFoundError("获取视频列表时发现用户不存在。")
            if result.get("statusCode") != 0:
                raise ServiceError(f"获取视频列表返回错误: {result.get('statusMsg')}")
            
            return filter_video_list(result)
        except Exception as e:
            raise ServiceError(f"获取用户(ID:{sec_uid})视频时发生错误: {e}") from e

    async def get_follow_list(self, sec_uid: str, cursor: int = 0, count: int = 30) -> Dict[str, Any]:
        url = "https://www.tiktok.com/api/user/list/"
        params = {"count": str(count), "maxCursor": "0", "minCursor": str(cursor), "secUid": sec_uid}
        request_url = self.join_url_params(url, params)

        try:
            response = await self.do_request("GET", request_url)
            result = response.json()
            if result.get("statusCode") in [10222, 10223]:
                raise UserNotFoundError(f"用户不存在或其关注列表私密 (API code: {result.get('statusCode')})")
            if result.get("statusCode") != 0:
                raise ServiceError(f"获取关注列表返回未处理的状态码: {result.get('statusCode')}")
            
            return filter_user_list(result)
        except Exception as e:
            raise ServiceError(f"获取关注列表时发生错误: {e}") from e

    async def get_follower_list(self, sec_uid: str, cursor: int = 0, count: int = 30) -> Dict[str, Any]:
        url = "https://www.tiktok.com/api/user/list/"
        params = {"count": str(count), "maxCursor": "0", "minCursor": str(cursor), "secUid": sec_uid, "scene": "67"}
        request_url = self.join_url_params(url, params)

        try:
            response = await self.do_request("GET", request_url)
            result = response.json()
            if result.get("statusCode") == 10222:
                raise UserNotFoundError(f"用户不存在或其粉丝列表私密 (API code: {result.get('statusCode')})")
            if result.get("statusCode") != 0:
                raise ServiceError(f"获取粉丝列表失败: {result.get('statusCode')}")
            
            return filter_user_list(result)
        except Exception as e:
            raise ServiceError(f"获取粉丝列表时发生错误: {e}") from e

    async def get_video_comments(self, video_id: str, cursor: int = 0, count: int = 50) -> Dict[str, Any]:
        url = "https://www.tiktok.com/api/comment/list/"
        params = {
            "aweme_id": video_id, "count": str(count), "cursor": str(cursor), "aid": "1988",
            "device_id": self.settings.default_device_id
        }
        request_url = self.join_url_params(url, params)
        try:
            response = await self.do_request("GET", request_url)
            result = response.json()

            if result.get("status_code") in [5, 10204]:
                 raise CommentNotFoundError(f"视频评论区关闭或视频不存在 (API code: {result.get('status_code')})")
            if result.get("status_code") != 0:
                raise ServiceError(f"获取评论列表失败 (API code: {result.get('status_code')})")
            if result.get("comments") is None:
                # 某些情况下，评论为空不是错误，而是正常状态
                return {"commentList": [], "cursor": "0", "hasMore": False, "statusCode": 0}
            
            return filter_comment_list(result)
        except Exception as e:
            raise ServiceError(f"获取评论时发生错误: {e}") from e

    async def get_comment_replies(self, comment_id: str, item_id: str, cursor: int = 0, count: int = 50) -> Dict[str, Any]:
        url = "https://www.tiktok.com/api/comment/list/reply/"
        params = {
            "aid": "1988", "comment_id": comment_id, "count": str(count),
            "cursor": str(cursor), "item_id": item_id, "device_id": self.settings.default_device_id
        }
        request_url = self.join_url_params(url, params)
        try:
            response = await self.do_request("GET", request_url)
            result = response.json()

            if result.get("status_code") in [5, 10204]:
                raise CommentNotFoundError(f"评论不存在或已被删除 (API code: {result.get('status_code')})")
            if result.get("status_code") != 0:
                raise ServiceError(f"获取评论回复列表失败 (API code: {result.get('status_code')})")
            
            cleaned_comments = remove_none_values(result.get("comments", []))
            return {
                "commentList": cleaned_comments, "cursor": result.get("cursor"),
                "has_more": result.get("has_more"), "total": result.get("total"),
                "statusCode": result.get("status_code")
            }
        except Exception as e:
            raise ServiceError(f"获取评论回复时发生错误: {e}") from e 