# 流程类，相关操作对基础类对应接口的封装
import uuid, re, json, time
from action_base import PublishBase
from typing import List
from utils import fix_mentions, parse_single_post_feature_info_text

class PublishFlow(PublishBase):
    def __init__(self, 
            config=None,
            unique_id="",
            cookies_dict={}, canvas_num=2010578131, 
            user_id="", user_sec_uid="", device_id="", WebIdLastTime="", # 这 4 入参为空，实例化时手动调用 async_init 获取
            publish_fileurl="", block_size=3145728, delay_publish_time=5, # 发布作品相关参数（发布的作品名称，每次上传的字节大小，延时请求发布接口的时间）
            publish_baseurl="", # 发布视频域名
            publish_filelist=[], # 发布视频上层封装，支持发布多个视频
            tz_name="", proxies=None
        ):
        super().__init__( 
            config=config,
            unique_id=unique_id,
            cookies_dict=cookies_dict, canvas_num=canvas_num, 
            user_id=user_id, user_sec_uid=user_sec_uid, device_id=device_id, WebIdLastTime=WebIdLastTime,
            publish_fileurl=publish_fileurl, block_size=block_size, delay_publish_time=delay_publish_time, # 发布作品相关参数（发布的作品名称，每次上传的字节大小，延时请求发布接口的时间）
            tz_name=tz_name, proxies=proxies
        )
        self.publish_baseurl = publish_baseurl
        self.publish_filelist = publish_filelist

    # 自动补充 关注/点赞/收藏 需要用到的用户信息
    def fill_follow_user_info(self, follow_user_info_dict, max_search_page=1, error_text=""):
        try:
            if not follow_user_info_dict.get("username"):
                return {"status": 0, "data": {"text": f'{self.unique_id} {error_text}', "response_data": ""}, "login_expired": 0}

            if (follow_user_info_dict.get("user_id")) and (follow_user_info_dict.get("sec_uid")):
                return follow_user_info_dict
            
            # """
            # 方案1，根据用户名在搜索里面获取
            get_index_page_result = self.get_index_page(search_unique_id=follow_user_info_dict.get("username"))
            if not get_index_page_result.get("status"):
                return {"status": 0, "data": {"text": f"{self.unique_id} 未提供完整的用户信息（user_id、sec_uid）", "response_data": get_index_page_result.get("data", {})}, "login_expired": 0}
            
            user_info = get_index_page_result.get("data", {}).get("response_data", {})
            if user_info.get("user_id") and user_info.get("sec_uid"):
                print(f"未提供完整的用户信息（user_id、sec_uid），自动填充成功：")
                print(json.dumps(user_info, indent=4, ensure_ascii=False))
                return {"status": 1, "data": {"text": f"{self.unique_id} 自动补充完整的用户信息（user_id、sec_uid）成功", "response_data": user_info}, "login_expired": 0}
            return {"status": 0, "data": {"text": f"{self.unique_id} 未提供完整的用户信息（user_id、sec_uid）", "response_data": get_index_page_result.get("data", {})}, "login_expired": 0}
            # """

            """ 
            # 方案2，根据用户名在搜索里面获取（有时忽然搜不到是因为需要过验证码，不稳定所以弃用此方案）
            search_id = ""
            for i in range(max_search_page):
                search_user_result = self.search_user(search_keyword=follow_user_info_dict.get("username"), cursor=i, search_id=search_id)
                if not search_user_result.get("status"):
                    return search_user_result
                
                search_users_list = search_user_result.get("data", {}).get("response_data", {}).get("search_users_list")
                search_id = search_user_result.get("data", {}).get("response_data", {}).get("search_id")
                parse_search_user_result = self.parse_search_user(search_users_list, search_keyword=follow_user_info_dict.get("username"), follow_user_info_dict=follow_user_info_dict)
                if ("关注" in error_text) and (parse_search_user_result.get("is_follow")):
                    return {"status": 0, "data": {"text": f"{self.unique_id} 该用户已关注", "response_data": ""}, "login_expired": 0}
                if not search_id:
                    return {"status": 0, "data": {"text": f"{self.unique_id} 未获取到 search_id，没有更多的该用户", "response_data": search_user_result}, "login_expired": 0}

                if parse_search_user_result.get("user_id") and parse_search_user_result.get("sec_uid"):
                    return {"status": 1, "data": {"text": f"{self.unique_id} 自动补充完整的用户信息（user_id、sec_uid）成功", "response_data": parse_search_user_result}, "login_expired": 0}
            else:
                return {"status": 0, "data": {"text": f"{self.unique_id} 未提供完整的用户信息（user_id、sec_uid）", "response_data": ""}, "login_expired": 0}
            """
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 未提供完整的用户信息（user_id、sec_uid），自动补充 关注/点赞/收藏 需要用到的用户信息出错：{e}", "response_data": ""}, "login_expired": 0}

    # 获取发布视频的作者信息
    def get_video_anchors_info(self, keyword="", creation_id="", page_size=10, max_page_num=5):
        anchors = []
        for index in range(max_page_num):
            get_poi_result = self.get_poi(keyword=keyword.replace(" ", ""), creation_id=creation_id, page_num=index+1, page_size=page_size)
            if not get_poi_result.get("status"):
                return get_poi_result
            
            response_json: dict = get_poi_result.get("data", {}).get("response_data", {})
            has_more = response_json.get("has_more", False)
            poi_list: List[dict] = response_json.get("poi_list", [])
            impr_id = response_json.get("log_pb", {}).get("impr_id", "")
            for poi_data in poi_list:
                name: str = poi_data.get("name", "")
                if name.lower().replace(" ", "") == keyword.lower().replace(" ", ""):
                    poi_id = poi_data.get("id", "")
                    poi_detail: dict = poi_data.get("detail", {})
                    collect_info = poi_detail.get("collect_info", "")
                    anchors.append({
                        "type": 45,
                        "keyword": name,
                        "language": "en",
                        "contentNew": json.dumps({
                            "poi_id": poi_id,
                            "fallback_address": poi_data.get("formatted_address", ""),
                            "city_code": poi_detail.get("city_code", ""),
                            "third_id": poi_id,
                            "region_code": poi_detail.get("country_code", ""),
                            "fallback_location": name,
                            "city_name": poi_detail.get("city_name", ""),
                            "is_claimed": False,
                            "fallback_lang": "en",
                            "is_rtl": False
                        }, separators=(",", ":")),
                        "__et_poi_info__": {
                            "poi_enter_method": "search_poi", # 还有一种推荐标签（"recommend_poi"），这里统一用搜索
                            "poi_id": poi_id,
                            "poi_search_id": impr_id,
                            "poi_log_id": impr_id,
                            "poi_is_cache_search": 0,
                            "poi_collect_info": collect_info
                        }
                    })
                    return anchors
            if not has_more:
                break
        return anchors
    
    # 构造发布视频需要的 data
    def create_publish_data(self, 
        upload_info={}, 
        video_desc="", 
        place_keyword="", 
        place_page_size=10, 
        place_max_page_num=5,
        set_schedule_timezone="",
        schedule_time="",
        visibility_type=0,
        allow_duet=1,
        allow_stitch=1,
        allow_comment=1,
        tcm_params={"commerce_toggle_info": {}},
        brand_organic_type=0,
        branded_content_type=0,
        ai_generated_content=False,
        copyright_music_check=False,
        content_check_id=True,
        user_id_map={"bolivzcvhib": "7468951440104899589"}
    ):
        try:
            if (visibility_type not in [0, 1, 2]):
                return {"status": 0, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 发布视频 data 创建失败：visibility_type 可见性要求（0：所有人，1：仅自己，2：所有朋友）", "response_data": visibility_type}, "login_expired": 0}

            if not all(value in [0, 1] for value in [allow_duet, allow_stitch, allow_comment]):
                return {"status": 0, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 发布视频 data 创建失败：allow_duet（二创）, allow_stitch（拼接）, allow_comment（评论）要求（0：不允许，1：允许）", "response_data": {"allow_duet": allow_duet, "allow_stitch": allow_stitch, "allow_comment": allow_comment}}, "login_expired": 0}

            if not all(value in [0, 2001] for value in [brand_organic_type, branded_content_type]):
                return {"status": 0, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 发布视频 data 创建失败：商业推广参数 brand_organic_type, branded_content_type 要求（0：不勾选，2001：勾选）", "response_data": {"brand_organic_type": brand_organic_type, "branded_content_type": branded_content_type}}, "login_expired": 0}

            if (branded_content_type) and (visibility_type == 1):
                return {"status": 0, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 发布视频 data 创建失败：设置了 Branded content（对应 branded_content_type） 就无法仅自己可见", "response_data": {"visibility_type": visibility_type, "branded_content_type": branded_content_type}}, "login_expired": 0}

            if (visibility_type == 1) and schedule_time:
                return {"status": 0, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 发布视频 data 创建失败：设置仅自己可见无法设置延时发布时间", "response_data": {"visibility_type": visibility_type, "schedule_time": schedule_time}}, "login_expired": 0}

            video_desc = fix_mentions(text=video_desc)
            print(f"视频描述处理结果：>>>{video_desc}<<<")

            creation_id = self.get_creation_id()
            data = {
                "post_common_info": {
                    "creation_id": creation_id,
                    "enter_post_page_from": 1,
                    "post_type": 3
                },
                "feature_common_info_list": [],
                "single_post_req_list": []
            }
            feature_common_info = {}
            if schedule_time:
                get_schedule_time_result = self.get_schedule_time(schedule_time, set_schedule_timezone)
                if not get_schedule_time_result.get("status"):
                    return get_schedule_time_result
                feature_common_info["schedule_time"] = get_schedule_time_result.get("data", {}).get("response_data", 0) # 延时发布视频

            feature_common_info.update({
                "geofencing_regions": [],
                "playlist_name": "",
                "playlist_id": ""
            })
            # 是否是推广宣传内容，个人品牌，Disclose post content
            if brand_organic_type:
                tcm_params["commerce_toggle_info"]["brand_organic_type"] = brand_organic_type # 勾选了个人品牌，Your brand
            if branded_content_type:
                tcm_params["commerce_toggle_info"]["branded_content_type"] = branded_content_type # 勾选了个人品牌内容（勾选即同意相关政策），Branded content
            feature_common_info["tcm_params"] = json.dumps(tcm_params, separators=(",", ":"))
            feature_common_info["sound_exemption"] = 0
            # 是否版权检查
            music_info = {}
            if copyright_music_check:
                chech_video_music_result = self.check_video_music(upload_info.get("vid"))
                if not chech_video_music_result.get("status"):
                    return chech_video_music_result
                music_info["music_pre_check_id"] = chech_video_music_result.get("data", {}).get("response_data", [])[1]
                feature_common_info["music_copyright"] = {"result": chech_video_music_result.get("data", {}).get("response_data", [])[0]}
            # 作者信息，就是地区的设置
            if place_keyword:
                feature_common_info["anchors"] = self.get_video_anchors_info(keyword=place_keyword, creation_id=creation_id, page_size=place_page_size, max_page_num=place_max_page_num)
            if ai_generated_content: # 是否开启 ai-generated content
                feature_common_info["aigc_info"] = {"aigc_label_type": 1}
            feature_common_info.update({
                "vedit_common_info": {
                    "draft": "",
                    "video_id": upload_info.get("vid")
                },
                "privacy_setting_info": {
                    "visibility_type": visibility_type, # 允许谁可见？0是所有人，1是仅自己可见，2是朋友可见
                    "allow_duet": allow_duet, # 允许二创
                    "allow_stitch": allow_stitch, # stitch：缝补
                    "allow_comment": allow_comment # 允许评论
                }
            })
            if content_check_id:
                feature_common_info["content_check_id"] = "" # 不清楚什么设置
            data["feature_common_info_list"].append(feature_common_info)

            # 事实上，markup_text 中的标签必须是用户选的，用户没选还是文本，哪怕 text 中手动输入格式 # 或 @，text_extra 会全都统一解析没问题
            # "waooo#girl@bolivzcvhib@funnystrange9#girls #beautiful girl" text_extra解析一样，在 markup_text 中" #beautiful girl" 当时浏览器上操作是文本
            parse_result = parse_single_post_feature_info_text(video_desc, user_id_map=user_id_map)
            if not parse_result.get("text_extra"):
                parse_result["markup_text"] = ""
            single_post_req = {
                "batch_index": 0,
                "video_id": upload_info.get("vid"),
                "is_long_video": 0,
                "single_post_feature_info": {
                    "text": video_desc,
                    "text_extra": parse_result.get("text_extra", []),
                    "markup_text": parse_result.get("markup_text", ""),
                    "music_info": music_info,
                    "poster_delay": 0,
                    "cloud_edit_video_height": upload_info.get("Height"),
                    "cloud_edit_video_width": upload_info.get("Width"),
                    "cloud_edit_is_use_video_canvas": False
                }
            }
            data["single_post_req_list"].append(single_post_req)
            return {"status": 1, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 发布视频 data 创建成功", "response_data": data}, "formate_timestamp": feature_common_info.get("schedule_time", 0), "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 发布视频 data 创建失败：{e}", "response_data": ""}, "login_expired": 0}

    # 发布视频
    def publish_video_flow(self, 
        video_info={},
        tcm_params={"commerce_toggle_info": {}}, # 默认或 有了 brand_organic_type、branded_content_type
        content_check_id=True, # 不知道什么东西，默认开启（其实好像有没有这个参数都无所谓）
        video_size=0 # 发布的每个视频的字节大小
    ):
        try:
            if not isinstance(video_info, dict):
                return {"status": 0, "data": {"text": f"{self.unique_id} 上传视频参数类型不支持", "response_data": video_info}, "login_expired": 0}
            
            # 如果传入新路径，发布新路径视频，否则发布 self 实例时的视频
            if not video_info.get("url", self.publish_fileurl):
                return {"status": 0, "data": {"text": f"{self.unique_id} 未提供需要上传的视频", "response_data": ""}, "login_expired": 0}
            
            publish_fileurl=f'{self.publish_baseurl}/{video_info.get("url", self.publish_fileurl)}'
            video_desc=video_info.get("description", "") # 视频描述
            pubilsh_data=video_info.get("pubilsh_data", {}) # 如果有手动组装好的 data
            block_size=video_info.get("block_size", self.block_size) # 分段上传最大字节数
            delay_publish_time=video_info.get("delay_publish_time", self.delay_publish_time) # 接口延时请求时间
            place_keyword=video_info.get("place_keyword", "") # 地区的设置
            place_page_size=video_info.get("place_page_size", 10) # 每页地区的数量
            place_max_page_num=video_info.get("place_max_page_num", 5) # 最大搜索地区页数
            set_schedule_timezone=video_info.get("set_schedule_timezone", self.tz_name) # 设置延时发布的时区
            schedule_time=video_info.get("schedule_time", "") # 视频延时（定时）发布时间，"年-月-日 时:分" 1.延时时间至少15min后 2.时针0~23整数 3.分针0~55整数 且 5 的倍数
            visibility_type=video_info.get("visibility_type", 0) # 作品的可见，所有人0，仅自己1，所有朋友2
            allow_duet=video_info.get("allow_duet", 1) # 是否允许二创，0,1
            allow_stitch=video_info.get("allow_stitch", 1) # 是否允许拼接，0,1
            allow_comment=video_info.get("allow_comment", 1) # 是否允许评论，0,1
            brand_organic_type=video_info.get("brand_organic_type", 0) # 是推广宣传视频，勾选了个人品牌 2001
            branded_content_type=video_info.get("branded_content_type", 0) # 是推广宣传视频，勾选了同意相关政策 2001
            ai_generated_content=video_info.get("ai_generated_content", False) # ai-generated content 的开关，ai 字幕
            copyright_music_check=video_info.get("copyright_music_check", False) # 版权检查开关，实际似乎只检查了音乐
            user_id_map=video_info.get("user_id_map", {"bolivzcvhib": "7468951440104899589"}) # 如果 video_desc 存在 @，需要把对应 {unique_id: user_id} 的查询字典封装，现在不传也会自动补全
            auto_parse_text=video_info.get("auto_parse_text", False) # text_extra、markup_text，是否自动解析，用于其他都是默认参数，但是 video_desc 存在 #、@ 的情况
            
            formate_timestamp = 0
            self.publish_fileurl = publish_fileurl
            update_base_videoinfo_result = self.update_base_videoinfo(video_info={"publish_videourl": self.publish_fileurl, "video_size": video_size})
            if not update_base_videoinfo_result.get("status"):
                return update_base_videoinfo_result
            
            base_videoinfo = update_base_videoinfo_result.get("data", {}).get("response_data", {})
            self.file_data = base_videoinfo["video_data"]
            self.file_size = base_videoinfo["video_size"]
            self.uploadid = str(uuid.uuid4()) # 上传视频给定的随机唯一标识
            self.block_size = block_size # 文件每次上传的字节大小
            self.current_time = ""
            self.x_amz_security_token = ""
            self.delay_publish_time = delay_publish_time # 最后一个接口发布作品需要有延时请求时间，协议发送会提示请求过快
            # 上传视频需要的次数，起始默认 1
            self.max_part_number = self.file_size // self.block_size
            if self.max_part_number < self.file_size / self.block_size:
                self.max_part_number += 1
            self.CRC32_list.clear()

            # /auth 接口
            get_auth_result = self.get_auth()
            if not get_auth_result.get("status"):
                return get_auth_result

            # /top/v1 接口
            get_publish_info_result = self.get_publish_info(get_auth_result.get("data", {}).get("response_data"))
            if not get_publish_info_result.get("status"):
                return get_publish_info_result
            
            # /upload/v1/
            # 大视频分段上传，最后提交一个 finish 状态请求
            # 小视频可以一次上传并完成，经测试用大视频上传方式最后提交 finish 也行
            for i in range(1, (self.max_part_number + 1)):
                while True:
                    put_video_result = self.put_video(
                        get_publish_info_result.get("data", {}).get("response_data"),
                        part_number=i,
                        part_offset=(i-1) * self.block_size
                    )
                    print(put_video_result)
                    if put_video_result.get("status"):
                        break

                    time.sleep(self.delay_publish_time)
                    self.max_fail_count -= 1
                    if self.max_fail_count <= 0:
                        return put_video_result
            
            finish_put_video_result = self.finish_put_video(
                get_auth_result.get("data", {}).get("response_data"),
                get_publish_info_result.get("data", {}).get("response_data"),
            )
            if not finish_put_video_result.get("status"):
                return finish_put_video_result
            upload_info=finish_put_video_result.get("data", {}).get("response_data") # 存放 vid（视频 id），视频高、宽
            
            time.sleep(self.delay_publish_time)
            # 组装 data 或 默认 data（仅文本描述）
            if (not pubilsh_data) and (schedule_time or brand_organic_type or branded_content_type or copyright_music_check or visibility_type or (not allow_duet) or (not allow_stitch) or (not allow_comment) or place_keyword or ai_generated_content or auto_parse_text):
                create_publish_data_result = self.create_publish_data(
                    upload_info=upload_info, 
                    video_desc=video_desc, 
                    place_keyword=place_keyword, # 地区的设置
                    place_page_size=place_page_size, 
                    place_max_page_num=place_max_page_num,
                    set_schedule_timezone=set_schedule_timezone,
                    schedule_time=schedule_time,
                    visibility_type=visibility_type, # 可见性设置，0, 1, 2
                    allow_duet=allow_duet, # 0，1
                    allow_stitch=allow_stitch, # 0，1
                    allow_comment=allow_comment, # 0，1
                    tcm_params=tcm_params,
                    brand_organic_type=brand_organic_type, # 0，2001
                    branded_content_type=branded_content_type, # 0，2001
                    ai_generated_content=ai_generated_content, # ai_generated_content 的开关
                    copyright_music_check=copyright_music_check, # 是否版权检查的开关
                    content_check_id=content_check_id,
                    user_id_map=user_id_map
                )
                if not create_publish_data_result.get("status"):
                    return create_publish_data_result
                pubilsh_data = create_publish_data_result.get("data", {}).get("response_data")
                formate_timestamp = create_publish_data_result.get("formate_timestamp", 0)

            # 发布视频 /project/post/v1/
            while True:
                publish_video_result = self.publish_video(
                    upload_info=upload_info, # 存放 vid（视频 id），视频高、宽
                    video_desc=video_desc,
                    pubilsh_data=pubilsh_data # 如果走了重新组装 data，优先采用此 data，没有组装则采用默认 data
                )
                if publish_video_result.get("status") != 2:
                    if not schedule_time:
                        formate_timestamp = publish_video_result.get("data", {}).get("response_data", {}).get("extra", {}).get("now", 0)
                    break
                self.max_fail_count -= 1
                if self.max_fail_count <= 0:
                    break
            # 下面的转换没有必要，除非知道用户客户端的时区，否则其实就是发布时间转时间戳再转回来
            # if formate_timestamp:
            #     formate_date = self.formate_timezone(input_timestamp=formate_timestamp, timezone=set_schedule_timezone)
            #     publish_video_result["formate_date"] = formate_date
            publish_video_result["formate_timestamp"] = formate_timestamp
            publish_video_result["timezone"] = set_schedule_timezone
            return publish_video_result
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 发布视频主流程出错：{e}", "response_data": ""}, "login_expired": 0}
        
if __name__ == "__main__":
    pass