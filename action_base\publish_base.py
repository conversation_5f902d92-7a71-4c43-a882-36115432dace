import json, random, time, uuid, pytz
from action_base.tkBase import TkBase
from datetime import datetime, timedelta
from utils import *
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from requests import Response

class PublishBase(TkBase):
    def __init__(self, 
            config=None,
            unique_id="", 
            cookies_dict={}, canvas_num=2010578131, 
            user_id="", user_sec_uid="", device_id="", WebIdLastTime="", # 这 4 入参为空，实例化时手动调用 sync_init 获取
            publish_fileurl="", block_size=3145728, delay_publish_time=5, # 发布作品相关参数（发布的作品名称，每次上传的字节大小，延时请求发布接口的时间）
            tz_name="", proxies=None
        ):
        super().__init__(
            config=config,
            unique_id=unique_id,
            cookies_dict=cookies_dict, canvas_num=canvas_num, 
            user_id=user_id, user_sec_uid=user_sec_uid, device_id=device_id, WebIdLastTime=WebIdLastTime,
            tz_name=tz_name, proxies=proxies
        )
        self.tk_upload_baseurl = ""
        self.publish_fileurl = publish_fileurl
        self.file_data = b''
        self.file_size = 0
        self.max_part_number = 0
        self.uploadid = str(uuid.uuid4()) # 上传视频给定的随机唯一标识
        self.CRC32_list = []
        self.block_size = block_size # 文件每次上传的字节大小
        self.current_time = ""
        self.x_amz_security_token = ""
        self.delay_publish_time = delay_publish_time # 最后一个接口发布作品需要有延时请求时间，协议发送会提示请求过快

    def get_creation_id(self):
        return "".join(random.choices("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_-", k=21)) # trackId

    def get_now_time(self):
        now = datetime.now(pytz.utc)
        return now.strftime('%Y-%m-%dT%H:%M:%SZ')
    
    def get_schedule_time(self, time_str: str, timezone: str) -> dict:
        try:
            # 时间格式校验，自动填充
            parts = time_str.split(" ")
            date_part, time_part = parts[0], parts[1]
            h, m = time_part.split(":")[:2]
            fill_time_str = f"{date_part} {h}:{m.zfill(2)}"

            dt = datetime.strptime(fill_time_str, "%Y-%m-%d %H:%M")
            tz = pytz.timezone(timezone)
            dt = tz.localize(dt)
            now = datetime.now(pytz.utc).astimezone(tz)
            if dt <= now + timedelta(minutes=15):
                return {"status": 0, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 延时时间至少要 15 min 以后", "response_data": ""}, "login_expired": 0}
            if dt.minute % 5 != 0 or dt.minute > 55:
                return {"status": 0, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 延时时间分钟需要满足 0~55 且是 5 的倍数", "response_data": ""}, "login_expired": 0}
            timestamp = int(dt.timestamp())
            return {"status": 1, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 延时时间满足要求", "response_data": timestamp}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 延时时间处理出错：{e}", "response_data": ""}, "login_expired": 0}

    def formate_timezone(self, input_timestamp: int, timezone: str="") -> str:
        if len(str(input_timestamp)) == 13:
            input_timestamp /= 1000
        
        if not timezone:
            timezone = self.tz_name
        tz = pytz.timezone(timezone)
        dt = datetime.fromtimestamp(input_timestamp, tz)
        return f"{dt.year}-{dt.month}-{dt.day} {dt.hour}:{dt.minute:02d}"

    # 获取 auth：需要 cookie sid_guard
    def get_auth(self):
        headers = {
            "User-Agent": self.ua,
            "Accept": "application/json, text/plain, */*",
            "sec-ch-ua-mobile": "?0",
            "sec-fetch-site": "same-origin",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://www.tiktok.com/tiktokstudio/upload",
            "accept-language": "en-US,en;q=0.9",
            "priority": "u=1, i"
        }
        try:
            response: "Response" = self.do_req(method="GET", url=self.join_url_params("https://www.tiktok.com/api/v1/video/upload/auth/", params={"aid": "1988"}), headers=headers)
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} /upload/auth 接口请求出错：{e}", "response_data": self.publish_fileurl}, "login_expired": 0}
        
        try:
            response_json: dict = response.json()
            status_msg: str = response_json.get("status_msg", "")
            video_token_v5 = response_json.get("video_token_v5", {})
            self.x_amz_security_token = video_token_v5.get("session_token", "")
            if status_msg.lower() == "login expired":
                return {"status": 0, "data": {"text": f"{self.unique_id} /upload/auth 接口获取 auth 信息失败：{self.publish_fileurl}，登录失效", "response_data": response_json}, "login_expired": 1}
            elif self.x_amz_security_token and video_token_v5:
                return {"status": 1, "data": {"text": f"{self.unique_id} /upload/auth 接口获取 auth 信息成功：{self.publish_fileurl}", "response_data": video_token_v5}, "login_expired": 0}
            return {"status": 0, "data": {"text": f"{self.unique_id} /upload/auth 接口获取 auth 信息失败：{self.publish_fileurl}", "response_data": response_json}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} {self.publish_fileurl} /upload/auth 接口获取 auth 信息响应内容解析出错：{e}", "response_data": ""}, "login_expired": 0}
        
    # /top/v1 接口，获取一些发布视频的 token
    def get_publish_info(self, video_token_v5, req_num=2): # 本来是请求两次，测试直接请求第2次也行
        # self.current_time = video_token_v5.get("current_time")
        self.current_time = self.get_now_time().replace("-", "").replace(":", "")
        params = {
            "Action": "GetUploadCandidates",
            "SpaceName": "tiktok",
            "Version": "2020-11-19", # 只是个版本号，固定的
            "X-Amz-Expires": "604800", # 固定
        }
        if req_num == 2:
            params = {
                "Action": "ApplyUploadInner",
                # "ClientBestHosts": self.tk_upload_baseurl,
                "FileSize": str(self.file_size), # 文件大小的字节数量
                "FileType": "video",
                "IsInner": "1",
                "SpaceName": "tiktok",
                "Version": "2020-11-19", # 只是个版本号，固定的
                "X-Amz-Expires": "604800", # 固定
                "device_platform": "web",
                "s": "".join(random.choices("abcdefghijklmnopqrstuvwxyz0123456789", k=random.randint(10, 11)))
            }
        Signature = get_Signature(
            secret_key=video_token_v5.get("secret_acess_key"),
            session_token=video_token_v5.get("session_token"),
            req_method="GET",
            url_path="/top/v1",
            params=params,
            req_body="",
            current_time=self.current_time
        )
        authorization = f'AWS4-HMAC-SHA256 Credential={video_token_v5.get("access_key_id", "")}/{self.current_time[0:8]}/US-TTP/vod/aws4_request, SignedHeaders=x-amz-date;x-amz-security-token, Signature={Signature}'
        headers = {
            "User-Agent": self.ua,
            "x-amz-security-token": self.x_amz_security_token,
            "x-amz-date": self.current_time,
            "authorization": authorization,
            "sec-ch-ua-mobile": "?0",
            "sec-fetch-site": "same-origin",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://www.tiktok.com/tiktokstudio/upload",
            "accept-language": "en-US,en;q=0.9",
            "priority": "u=1, i"
        }
        try:
            response: "Response" = self.do_req(method="GET", url=self.join_url_params("https://www.tiktok.com/top/v1", params), headers=headers)
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} /top/v1 接口请求出错：{e}", "response_data": self.publish_fileurl}, "login_expired": 0}
        
        try:
            response_json: dict = response.json()
            video_url_path = response_json.get("Result", {}).get("InnerUploadAddress", {}).get("UploadNodes", [])[0].get("StoreInfos", [])[0].get("StoreUri", "")
            auth_value = response_json.get("Result", {}).get("InnerUploadAddress", {}).get("UploadNodes", [])[0].get("StoreInfos", [])[0].get("Auth", "")
            session_key = response_json.get("Result", {}).get("InnerUploadAddress", {}).get("UploadNodes", [])[0].get("SessionKey", [])
            self.tk_upload_baseurl = response_json.get("Result", {}).get("InnerUploadAddress", {}).get("UploadNodes", [])[0].get("UploadHost", "")
            if not self.tk_upload_baseurl:
                return {"status": 0, "data": {"text": f"{self.unique_id} /top/v1 接口获取视频信息失败：{self.publish_fileurl}，未能获取到视频上传地址的域名（self.tk_upload_baseurl）", "response_data": response_json}, "login_expired": 0}

            if video_url_path and auth_value and session_key:
                video_info = {
                    "video_url_path": video_url_path,
                    "Authorization": auth_value,
                    "session_key": session_key,
                }
                return {"status": 1, "data": {"text": f"{self.unique_id} /top/v1 接口获取视频信息成功：{self.publish_fileurl}", "response_data": video_info}, "login_expired": 0}
            return {"status": 0, "data": {"text": f"{self.unique_id} /top/v1 接口获取视频信息失败：{self.publish_fileurl}", "response_data": response_json}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} {self.publish_fileurl} /top/v1 接口响应内容解析出错：{e}", "response_data": ""}, "login_expired": 0}
        
    # 发布视频的版权检测（似乎主要是检测音乐）
    def check_video_music(self, video_id):
        headers = {
            "User-Agent": self.ua,
            "Accept": "application/json, text/plain, */*",
            "sec-ch-ua-mobile": "?0",
            "sec-fetch-site": "same-origin",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://www.tiktok.com/tiktokstudio/upload?from=webapp&lang=en",
            "accept-language": "zh-CN,zh;q=0.9",
            "priority": "u=1, i"
        }
        params = {
            "video_id": video_id,
            "aid": "1988"
        }
        try:
            response: "Response" = self.do_req(method="GET", url=self.join_url_params("https://www.tiktok.com/tiktok/copyright/music/check/v1/", params=params), headers=headers)
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 视频音乐版权检查请求出错：{e}", "response_data": self.publish_fileurl}, "login_expired": 0}

        try:
            response_json: dict = response.json()
            copyright_detection_result = response_json.get("copyright_detection_result", 0)
            pre_check_id = response_json.get("pre_check_id", "")
            if copyright_detection_result and pre_check_id:
                return {"status": 1, "data": {"text": f"{self.unique_id} 视频音乐版权检查成功：{self.publish_fileurl}", "response_data": [copyright_detection_result, pre_check_id]}, "login_expired": 0}
            elif pre_check_id and (not copyright_detection_result):
                return {"status": 0, "data": {"text": f"{self.unique_id} 视频音乐存在版权问题：{self.publish_fileurl}", "response_data": response_json}, "login_expired": 0}
            return {"status": 0, "data": {"text": f"{self.unique_id} 视频音乐版权检查失败：{self.publish_fileurl}", "response_data": response_json}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 视频音乐版权检查响应内容解析出错：{e}", "response_data": ""}, "login_expired": 0}

    # 分段上传视频
    def put_video(self, video_info, part_number=1, part_offset=0):
        # 最后一个视频片段长度
        if (part_offset + self.block_size) >= self.file_size:
            self.block_size = self.file_size - part_offset

        CRC32_value = get_CRC32(start_index=part_offset, block_size=self.block_size, file_data=self.file_data)
        self.CRC32_list.append(CRC32_value)
        headers = {
            "User-Agent": self.ua,
            "Content-Type": "application/octet-stream",
            "Pragma": "no-cache",
            "Cache-Control": "no-cache",
            "Authorization": video_info.get("Authorization"),
            "Content-CRC32": CRC32_value, 
            "sec-ch-ua-mobile": "?0",
            "X-Storage-U": self.user_id,
            "Content-Disposition": "attachment; filename=\"undefined\"",
            "Origin": "https://www.tiktok.com",
            "Sec-Fetch-Site": "cross-site",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://www.tiktok.com/",
            "Accept-Language": "en-US,en;q=0.9"
        }
        params = {
            "uploadid": self.uploadid,
            "part_number": str(part_number), # 切片数量依次索引
            "phase": "transfer",
            "part_offset": str(part_offset), # 上一次的偏移+上传传输的size就是本次的偏移
            "uploadmode": "stream",
            "enable_omit_initupload": "1", # 可能是起始的 part_number
            "size": str(self.block_size),
            "offset": "0"
        }
        try:
            response: "Response" = self.do_req(
                method="POST", 
                url=self.join_url_params(
                    f'https://{self.tk_upload_baseurl}/upload/v1/{video_info.get("video_url_path")}',
                    params=params
                ), 
                headers=headers, 
                data=self.file_data[part_offset: part_offset + self.block_size]
            )
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 上传视频请求出错：{e}", "response_data": self.publish_fileurl}, "login_expired": 0}
        
        try:
            response_json: dict = response.json()
            if (response_json.get("message").lower() == "success"):
                return {"status": 1, "data": {"text": f"{self.unique_id} 上传视频（总大小：{self.file_size}） {self.publish_fileurl} 片段 {part_offset}~{part_offset+self.block_size} 成功", "response_data": response_json}, "login_expired": 0}
            return {"status": 0, "data": {"text": f"{self.unique_id} 上传视频（总大小：{self.file_size}） {self.publish_fileurl} 片段 {part_offset}~{part_offset+self.block_size} 失败", "response_data": response_json}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 上传视频响应内容解析出错：{e}", "response_data": ""}, "login_expired": 0}
        
    # 视频上传完成后的请求
    def finish_put_video(self, video_token_v5, video_info):
        headers = {
            "User-Agent": self.ua,
            "Content-Type": "application/json",
            "Authorization": video_info.get("Authorization"),
            "sec-ch-ua-mobile": "?0",
            "X-Upload-With-PostUpload": "1",
            "X-Storage-U": self.user_id,
            "Origin": "https://www.tiktok.com",
            "Sec-Fetch-Site": "cross-site",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://www.tiktok.com/",
            "Accept-Language": "en-US,en;q=0.9"
        }
        params = {
            "uploadmode": "stream",
            "phase": "finish",
            "uploadid": self.uploadid,
            "size": str(self.file_size)
        }
        parts_crc = ""
        for index in range(len(self.CRC32_list)):
            parts_crc += f"{index+1}:{self.CRC32_list[index]},"
        parts_crc = parts_crc[:-1]
        data = {
            "parts_crc": parts_crc,
            "post_upload_param": {
                "sts2_token": self.x_amz_security_token,
                "sts2_secret": video_token_v5.get("secret_acess_key"),
                "session_key": video_info.get("session_key"),
                "functions": []
            }
        }
        data = json.dumps(data, separators=(',', ':'))
        try:
            response: "Response" = self.do_req(method="POST", url=self.join_url_params(f'https://{self.tk_upload_baseurl}/upload/v1/{video_info.get("video_url_path")}', params=params), headers=headers, data=data)
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 上传视频完成请求出错：{e}", "response_data": self.publish_fileurl}, "login_expired": 0}

        try:
            response_json: dict = response.json()
            if response_json.get("message").lower() == "success":
                upload_info = {
                    "vid": response_json.get("data", {}).get("post_upload_resp", {}).get("results", [])[0].get("vid"),
                    "Height": response_json.get("data", {}).get("post_upload_resp", {}).get("results", [])[0].get("video_meta", {}).get("OriginHeight"),
                    "Width": response_json.get("data", {}).get("post_upload_resp", {}).get("results", [])[0].get("video_meta", {}).get("OriginWidth"),
                }
                return {"status": 1, "data": {"text": f"{self.unique_id} 上传视频完成请求成功：{self.publish_fileurl}", "response_data": upload_info}, "login_expired": 0}
            return {"status": 0, "data": {"text": f"{self.unique_id} 上传视频完成请求失败：{self.publish_fileurl}", "response_data": response_json}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 上传视频完成响应内容解析出错：{e}", "response_data": ""}, "login_expired": 0}
        
    # 发布视频的 anchors 信息
    def get_poi(self, keyword="", creation_id="", page_num=1, page_size=10):
        headers = {
            "User-Agent": self.ua,
            "Accept": "application/json, text/plain, */*",
            "Content-Type": "application/json",
            "sec-ch-ua-mobile": "?0",
            "origin": "https://www.tiktok.com",
            "sec-fetch-site": "same-origin",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://www.tiktok.com/tiktokstudio/upload?from=webapp&lang=en",
            "accept-language": "en-US,en;q=0.9",
            "priority": "u=1, i"
        }
        data = {
            "page_num": page_num,
            "page_size": page_size,
            "keyword": keyword,
            "search_params": {
                "search_id": "",
                "creation_id": creation_id,
                "last_search_id": ""
            },
            "web_params": {
                "aid": 1988,
                "app_name": "tiktok_web",
                "channel": "tiktok_web",
                "device_platform": "web_pc",
                "device_id": self.device_id,
                "region": "US",
                "priority_region": "US",
                "referer": f"https://www.tiktok.com/@{self.unique_id}?lang=en",
                "root_referer": "https://www.tiktok.com/",
                "cookie_enabled": True,
                "screen_width": 1920,
                "screen_height": 1080,
                "browser_language": "en-US",
                "browser_platform": "Win32",
                "browser_name": self.ua.split("/")[0], # Mozilla,
                "browser_version": self.BROWSER_VERSION, # '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
                "browser_online": True,
                # "verifyFp": "verify_m8o61c6t_Yt06C2G1_jXCb_4HA6_99tQ_DXS9DqxY2SMM",
                "app_language": "en",
                "webcast_language": "en",
                "tz_name": self.tz_name,
                "is_fullscreen": False,
                # "history_len": 7
            }
        }
        data = json.dumps(data, separators=(',', ':'))
        try:
            response: "Response" = self.do_req(method="POST", url=self.join_url_params("https://www.tiktok.com/tiktok/v1/creator/poi/list", params={"aid": "1988"}), headers=headers, data=data)
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 获取坐标 poi 请求出错：{e}", "response_data": self.publish_fileurl}, "login_expired": 0}
        
        try:
            response_json: dict = response.json()
            if (not response_json.get("status_msg", "")) and response_json.get("poi_list", []):
                return {"status": 1, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 获取坐标 poi 成功", "response_data": response_json}, "login_expired": 0}
            return {"status": 0, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 获取坐标 poi 失败", "response_data": response_json}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 获取坐标 poi 接口响应内容解析出错：{e}", "response_data": ""}, "login_expired": 0}

    # 发布视频的版权检测（似乎主要是检测音乐）
    def check_video_music(self, video_id):
        headers = {
            "User-Agent": self.ua,
            "Accept": "application/json, text/plain, */*",
            "sec-ch-ua-mobile": "?0",
            "sec-fetch-site": "same-origin",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://www.tiktok.com/tiktokstudio/upload?from=webapp&lang=en",
            "accept-language": "zh-CN,zh;q=0.9",
            "priority": "u=1, i"
        }
        params = {
            "video_id": video_id,
            "aid": "1988"
        }
        try:
            response: "Response" = self.do_req(method="GET", url=self.join_url_params("https://www.tiktok.com/tiktok/copyright/music/check/v1/", params=params), headers=headers)
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 视频音乐版权检查请求出错：{e}", "response_data": self.publish_fileurl}, "login_expired": 0}

        try:
            response_json: dict = response.json()
            copyright_detection_result = response_json.get("copyright_detection_result", 0)
            pre_check_id = response_json.get("pre_check_id", "")
            if copyright_detection_result and pre_check_id:
                return {"status": 1, "data": {"text": f"{self.unique_id} 视频音乐版权检查成功：{self.publish_fileurl}", "response_data": [copyright_detection_result, pre_check_id]}, "login_expired": 0}
            elif pre_check_id and (not copyright_detection_result):
                return {"status": 0, "data": {"text": f"{self.unique_id} 视频音乐存在版权问题：{self.publish_fileurl}", "response_data": response_json}, "login_expired": 0}
            return {"status": 0, "data": {"text": f"{self.unique_id} 视频音乐版权检查失败：{self.publish_fileurl}", "response_data": response_json}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} {self.publish_fileurl} 视频音乐版权检查响应内容解析出错：{e}", "response_data": ""}, "login_expired": 0}

    # 发布视频
    def publish_video(self, upload_info={}, video_desc="", pubilsh_data={}):
        now_time = int(time.time())
        url = "https://www.tiktok.com/tiktok/web/project/post/v1/"
        headers = {
            "User-Agent": self.ua,
            "Accept": "application/json, text/plain, */*",
            "Content-Type": "application/json",
            "pragma": "no-cache",
            "cache-control": "no-cache",
            "sec-ch-ua-mobile": "?0",
            "origin": "https://www.tiktok.com",
            "sec-fetch-site": "same-origin",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://www.tiktok.com/tiktokstudio/upload?from=webapp",
            "accept-language": "en-US,en;q=0.9",
            "priority": "u=1, i"
        } # tt-ticket-guard-client-data 及相关参数有时有有时没有，可不带，带了效果一样
        params = {
            "app_name": "tiktok_web",
            "channel": "tiktok_web",
            "device_platform": "web",
            "tz_name": self.tz_name, # Intl.DateTimeFormat().resolvedOptions().timeZone
            "aid": "1988",
            "msToken": self.session.cookies.get_dict().get("msToken")
        }
        if not pubilsh_data:
            data = {
                "post_common_info": {
                    "creation_id": self.get_creation_id(),
                    "enter_post_page_from": 1,
                    "post_type": 3
                },
                "feature_common_info_list": [
                    {
                        "geofencing_regions": [],
                        "playlist_name": "",
                        "playlist_id": "",
                        "tcm_params": json.dumps({"commerce_toggle_info": {}}, separators=(",", ":")),
                        "sound_exemption": 0,
                        "anchors": [],
                        "vedit_common_info": {
                            "draft": "",
                            "video_id": upload_info.get("vid")
                        },
                        "privacy_setting_info": {
                            "visibility_type": 0, # 允许谁可见？0是所有人，1是仅自己可见，2是朋友可见
                            "allow_duet": 1, # 允许二创
                            "allow_stitch": 1, # stitch：缝补
                            "allow_comment": 1 # 允许评论
                        },
                        "content_check_id": ""
                    }
                ],
                "single_post_req_list": [
                    {
                        "batch_index": 0,
                        "video_id": upload_info.get("vid"),
                        "is_long_video": 0,
                        "single_post_feature_info": {
                            "text": video_desc,
                            "text_extra": [],
                            "markup_text": video_desc,
                            "music_info": {},
                            "poster_delay": 0,
                            "cloud_edit_video_height": upload_info.get("Height"),
                            "cloud_edit_video_width": upload_info.get("Width"),
                            "cloud_edit_is_use_video_canvas": False
                        }
                    }
                ]
            }
        else:
            data = pubilsh_data
        X_Bogus = get_XBogus(
            params=params, 
            data=data, 
            ua=self.ua, 
            now_time=now_time,
            canvas_num=self.canvas_num,
            extra_num=128
        )
        _signature = get_signature(
            url=url, 
            params=params, 
            X_Bogus=X_Bogus, 
            data=data, 
            ua=self.ua, 
            now_time=now_time,
            location_href="https://www.tiktok.com/tiktokstudio/upload?from=webapp",
            canvas_num=self.canvas_num
        )
        params["X-Bogus"] = X_Bogus
        params["_signature"] = _signature
        data = json.dumps(data, separators=(',', ':'))
        try:
            response: "Response" = self.do_req(method="POST", url=self.join_url_params(url, params), headers=headers, data=data)
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 发布视频 {self.publish_fileurl} 请求出错：{e}", "response_data": ""}, "login_expired": 0}
        
        try:
            response_json: dict = response.json()
            # "Permission Denied" 重复提交
            if response_json.get("status_msg").lower() in ["invalid parameters", "permission denied"]:
                return {"status": 0, "data": {"text": f"{self.unique_id} 发布视频 {self.publish_fileurl} 失败", "response_data": response_json}, "login_expired": 0}
            # "server is currently unavailable. please try again later." 请求过快，最后发布的接口得延时
            elif response_json.get("status_msg").lower() == "server is currently unavailable. please try again later.":
                return {"status": 0, "data": {"text": f"{self.unique_id} 发布视频 {self.publish_fileurl} 失败", "response_data": response_json}, "login_expired": 0}
            elif response_json.get("project_status", "") and response_json.get("project_id", ""):
                single_post_resp = response_json["single_post_resp_list"][0] if (len(response_json.get("single_post_resp_list", [])) == 1) else response_json["single_post_resp_list"]
                result = {"status": 1, "data": {"text": f"{self.unique_id} 发布视频 {self.publish_fileurl} 成功", "response_data": response_json}, "login_expired": 0}
                if isinstance(single_post_resp, dict):
                    result.update(single_post_resp)
                else:
                    result.update({"single_post_resp_list": single_post_resp})
                return result
            return {"status": 0, "data": {"text": f"{self.unique_id} 发布视频 {self.publish_fileurl} 失败，未知情况", "response_data": response_json}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 发布视频 {self.publish_fileurl} 接口响应内容解析出错：{e}", "response_data": ""}, "login_expired": 0}