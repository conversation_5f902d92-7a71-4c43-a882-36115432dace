import json, random, time
from action_base.tkBase import TkBase
from utils import *
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from requests import Response

class ModifyBase(TkBase):
    def __init__(self, 
            config=None,
            unique_id="",
            profile_img_url="", # 修改头像的图片地址
            cookies_dict={}, canvas_num=2010578131, 
            user_id="", user_sec_uid="", device_id="", WebIdLastTime="", # 这 4 入参为空，实例化时手动调用 async_init 获取
            tz_name="", proxies=None
        ):
        super().__init__(
            config=config,
            unique_id=unique_id,
            cookies_dict=cookies_dict, canvas_num=canvas_num, 
            user_id=user_id, user_sec_uid=user_sec_uid, device_id=device_id, WebIdLastTime=WebIdLastTime,
            tz_name=tz_name, proxies=proxies
        )
        self.profile_img_url = profile_img_url # 需要修改的头像名称
        self.profile_data = b'' # 头像的字节数据
        self.mime_type = "" # 头像的图片类型

    def get_settings(self):
        now_time = int(time.time() * 1000)
        headers = {
            "accept": "*/*",
            "accept-language": "en-US,en;q=0.9",
            "cache-control": "no-cache",
            "pragma": "no-cache",
            "priority": "u=1, i",
            "referer": f"https://www.tiktok.com/@{self.unique_id}",
            "sec-ch-ua-mobile": "?0",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": self.ua
        }
        params = {
            "WebIdLastTime": self.WebIdLastTime,
            "aid": "1988",
            "app_language": "en",
            "app_name": "tiktok_web",
            "browser_language": "en-US",
            "browser_name": self.ua.split("/")[0], # Mozilla,
            "browser_online": "true",
            "browser_platform": "Win32",
            "browser_version": self.BROWSER_VERSION, # '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            "channel": "tiktok_web",
            "cookie_enabled": "true",
            "data_collection_enabled": "true",
            "device_id": self.device_id,
            "device_platform": "web_pc",
            "focus_state": "false",
            "from_page": "user",
            # "history_len": "5",
            "is_fullscreen": "false",
            "is_page_visible": "false",
            "odinId": self.user_id,
            "os": "windows",
            "priority_region": "US",
            "referer": "",
            "region": "US",
            "screen_height": "1080",
            "screen_width": "1920",
            "tz_name": self.tz_name,
            "user_is_login": "true",
            "webcast_language": "en",
            "msToken": self.session.cookies.get_dict().get("msToken")
        }
        params, data = group_XBogus_XGnarly(
            params=params,
            data="",
            no_json=True, 
            ua=self.ua,
            now_time=now_time,
            canvas_num=self.canvas_num, 
            extra_num=14,
            page_sign=self.page_sign
        )
        try:
            response: "Response" = self.do_req(method="GET", url=self.join_url_params("https://www.tiktok.com/api/user/settings/", params=params), headers=headers)
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} settings 接口请求出错：{e}", "response_data": ""}, "login_expired": 0}
        
        try:
            response_json: dict = response.json()
            settings = response_json.get("settings", {})
            status_msg = response_json.get("status_msg", "")
            if settings and not status_msg:
                return {"status": 1, "data": {"text": f"{self.unique_id} 请求 settings 成功", "response_data": settings}, "login_expired": 0}
            elif status_msg.lower() == 'not logged in':
                return {"status": 0, "data": {"text": f"{self.unique_id} 请求 settings 失败，登录失效", "response_data": response_json}, "login_expired": 1}
            return {"status": 0, "data": {"text": f"{self.unique_id} 请求 settings 失败", "response_data": response_json}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 请求 settings 响应内容解析出错：{e}", "response_data": ""}, "login_expired": 0}
        
    # 更改唯一 id 前检查是否可使用
    def check_unique_id(self, new_unique_id):
        now_time = int(time.time() * 1000)
        url = "https://www.tiktok.com/api/uniqueid/check/"
        headers = {
            "User-Agent": self.ua,
            "tt-csrf-token": self.session.cookies.get_dict().get("tt_csrf_token"),
            "sec-ch-ua-mobile": "?0",
            "sec-fetch-site": "same-origin",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": f"https://www.tiktok.com/@{self.unique_id}",
            "accept-language": "en-US,en;q=0.9",
            "priority": "u=1, i"
        }
        params = {
            "WebIdLastTime": self.WebIdLastTime,
            "aid": "1988",
            "app_language": "en",
            "app_name": "tiktok_web",
            "browser_language": "en-US",
            "browser_name": self.ua.split("/")[0], # Mozilla,
            "browser_online": "true",
            "browser_platform": "Win32",
            "browser_version": self.BROWSER_VERSION, # '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            "channel": "tiktok_web",
            "cookie_enabled": "true",
            "data_collection_enabled": "true",
            "device_id": self.device_id,
            "device_platform": "web_pc",
            "focus_state": "true",
            "from_page": "user",
            # "history_len": "15",
            "is_fullscreen": "false",
            "is_page_visible": "true",
            "odinId": self.user_id,
            "os": "windows",
            "priority_region": "US",
            "referer": "",
            "region": "US",
            "screen_height": "1080",
            "screen_width": "1920",
            "tz_name": self.tz_name,
            "unique_id": new_unique_id,
            "user_is_login": "true",
            "webcast_language": "en",
            "msToken": self.session.cookies.get_dict().get("msToken")
        }
        params, data = group_XBogus_XGnarly(
            params=params,
            data="",
            no_json=True, 
            ua=self.ua,
            now_time=now_time,
            canvas_num=self.canvas_num, 
            extra_num=0,
            page_sign=self.page_sign
        )
        try:
            response: "Response" = self.do_req(method="GET", url=self.join_url_params(url, params=params), headers=headers)
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 检查新 unique_id 是否唯一 接口请求出错：{e}", "response_data": ""}, "login_expired": 0}
        
        try:
            response_json: dict = response.json()
            is_valid = response_json.get("is_valid", "")
            recommended_unique_ids = response_json.get("recommended_unique_ids", [])
            if is_valid:
                return {"status": 1, "data": {"text": f"{self.unique_id} 更改唯一 id {new_unique_id} 可以使用，推荐其他可使用的名称：{recommended_unique_ids}", "response_data": recommended_unique_ids}, "login_expired": 0}
            elif response_json.get("status_msg", ""):
                return {"status": 0, "data": {"text": f"{self.unique_id} 更改唯一id {new_unique_id} 已被使用，推荐其他可使用的名称：{recommended_unique_ids}", "response_data": recommended_unique_ids}, "login_expired": 0}
            return {"status": 0, "data": {"text": f"{self.unique_id} 检查新 unique_id 是否唯一失败", "response_data": response_json}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 检查新 unique_id 是否唯一接口响应内容解析出错：{e}", "response_data": ""}, "login_expired": 0}

    # 更改唯一 id
    def change_unique_id(self, new_unique_id):
        now_time = int(time.time() * 1000)
        url = "https://www.tiktok.com/passport/web/login_name/update/"
        headers = {
            "user-agent": self.ua,
            "Accept": "application/json, text/javascript",
            "accept-language": "en-US,en;q=0.9",
            "Content-Type": "application/x-www-form-urlencoded",
            "x-secsdk-csrf-token": "DOWNGRADE",
        }
        params = {
            "multi_login": "1",
            "did": self.device_id,
            "aid": "1459",
            "account_sdk_source": "web",
            "sdk_version": "2.1.5-tiktokbeta.1",
            "language": "en",
            "shark_extra": json.dumps({
                "aid": 1459,
                "app_name": "Tik_Tok_Login",
                "channel": "tiktok_web",
                "device_platform": "web_pc",
                "device_id": self.device_id,
                "region": "US",
                "priority_region": "US",
                "os": "windows",
                "referer": "",
                "cookie_enabled": True,
                "screen_width": 1920,
                "screen_height": 1080,
                "browser_language": "en-US",
                "browser_platform": "Win32",
                "browser_name": self.ua.split("/")[0], # Mozilla,
                "browser_version": self.BROWSER_VERSION, # '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                "browser_online": True,
                "app_language": "en",
                "webcast_language": "en",
                "tz_name": self.tz_name,
                "is_page_visible": True,
                "focus_state": True,
                "is_fullscreen": False,
                "history_len": 7,
                "user_is_login": True,
                "data_collection_enabled": True
            }, separators=(",", ":")),
            "msToken": self.session.cookies.get_dict().get("msToken")
        }
        data = {"login_name": new_unique_id}
        params, data = group_XBogus_XGnarly(
            params=params,
            data=data,
            no_json=True, 
            ua=self.ua,
            now_time=now_time,
            canvas_num=self.canvas_num, 
            extra_num=0,
            page_sign=self.page_sign
        )
        try:
            response: "Response" = self.do_req(method="POST", url=self.join_url_params(url, params=params), headers=headers, data=data)
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 修改个人 unique_id 接口请求出错：{e}", "response_data": ""}, "login_expired": 0}
        
        try:
            response_json: dict = response.json()
            response_message: str = response_json.get("message", "")
            if response_message.lower() == "success":
                self.unique_id = new_unique_id
                return {"status": 1, "data": {"text": f"{self.unique_id} 修改个人 unique_id ：{new_unique_id} 成功", "response_data": response_json}, "login_expired": 0}
            elif response_message.lower() == "error":
                return {"status": 0, "data": {"text": f'{self.unique_id} 修改个人 unique_id ：{new_unique_id} 失败，原因：{response_json.get("data", {}).get("description", "")}', "response_data": response_json}, "login_expired": 0}
            return {"status": 0, "data": {"text": f'{self.unique_id} 修改个人 unique_id ：{new_unique_id} 失败', "response_data": response_json}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 修改个人 unique_id 接口响应内容解析出错：{e}", "response_data": ""}, "login_expired": 0}
        
    # 上传头像图片，获取上传后的链接
    def upload_profile_pic(self):
        boundary = f'----WebKitFormBoundary{"".join(random.choices("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789", k=16))}'
        url = "https://www.tiktok.com/api/upload/image/"
        headers = {
            "User-Agent": self.ua,
            "pragma": "no-cache",
            "cache-control": "no-cache",
            "x-secsdk-csrf-token": "DOWNGRADE",
            "content-type": f"multipart/form-data; boundary={boundary}",
            "sec-ch-ua-mobile": "?0",
            "origin": "https://www.tiktok.com",
            "sec-fetch-site": "same-origin",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": f"https://www.tiktok.com/@{self.unique_id}",
            "accept-language": "en-US,en;q=0.9",
            "priority": "u=1, i"
        }
        params = {
            "WebIdLastTime": self.WebIdLastTime,
            "aid": "1988",
            "app_language": "en",
            "app_name": "tiktok_web",
            "browser_language": "en-US",
            "browser_name": self.ua.split("/")[0], # Mozilla,
            "browser_online": "true",
            "browser_platform": "Win32",
            "browser_version": self.BROWSER_VERSION, # '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            "channel": "tiktok_web",
            "cookie_enabled": "true",
            "data_collection_enabled": "true",
            "device_id": self.device_id,
            "device_platform": "web_pc",
            "focus_state": "true",
            "from_page": "user",
            "is_fullscreen": "false",
            "is_page_visible": "true",
            "odinId": self.user_id,
            "os": "windows",
            "priority_region": "US",
            "referer": "",
            "region": "US",
            "screen_height": "1080",
            "screen_width": "1920",
            "tz_name": self.tz_name,
            "user_is_login": "true",
            "webcast_language": "en"
        }
        profile_img_name = self.profile_img_url.split("/")[-1]
        data = (
            f"--{boundary}\r\n"
            f'Content-Disposition: form-data; name="file"; filename="{profile_img_name}"\r\n'
            f"Content-Type: {self.mime_type}\r\n\r\n"
        ).encode('utf-8') + self.profile_data + (
            f"\r\n--{boundary}\r\n"
            f'Content-Disposition: form-data; name="source"\r\n\r\n'
            f"0\r\n"
            f"--{boundary}--\r\n"
        ).encode('utf-8')
        try:
            response: "Response" = self.do_req(method="POST", url=self.join_url_params(url, params=params), headers=headers, data=data)
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 头像 {self.profile_img_url} 上传接口请求出错：{e}", "response_data": ""}, "login_expired": 0}
        
        try:
            response_json: dict = response.json()
            profile_pic_uri = response_json.get("data", {}).get("uri", "")
            if profile_pic_uri:
                return {"status": 1, "data": {"text": f"{self.unique_id} 头像 {self.profile_img_url} 上传成功", "response_data": profile_pic_uri}, "login_expired": 0}
            return {"status": 0, "data": {"text": f"{self.unique_id} 头像 {self.profile_img_url} 上传失败", "response_data": response_json}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 头像 {self.profile_img_url} 上传接口响应内容解析出错：{e}", "response_data": ""}, "login_expired": 0}
    
    # 改个人信息，其中个人简介 80 字上限（tk 未知风控，协议容易掉线）
    def change_info(self, avatar_uri="", new_nickname="", signature_text=""):
        time.sleep(3) # 前置接口可能是这个：https://www.tiktok.com/api/user/settings/

        now_time = int(time.time() * 1000)
        url = "https://www.tiktok.com/api/update/profile/"
        headers = {
            "accept": "*/*",
            "accept-language": "en-US,en;q=0.9",
            "cache-control": "no-cache",
            "content-type": "application/x-www-form-urlencoded",
            "origin": "https://www.tiktok.com",
            "pragma": "no-cache",
            "priority": "u=1, i",
            "referer": f"https://www.tiktok.com/@{self.unique_id}",
            "sec-ch-ua-mobile": "?0",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "tt-csrf-token": self.session.cookies.get_dict().get("tt_csrf_token"),
            "user-agent": self.ua,
            "x-cthulhu-csrf": "1",
        }
        params = {
            "WebIdLastTime": self.WebIdLastTime, # 应该是登录后的标记时间，一直固定
            "aid": "1988",
            "app_language": "en",
            "app_name": "tiktok_web",
            "browser_language": "en-US",
            "browser_name": self.ua.split("/")[0], # Mozilla,
            "browser_online": "true",
            "browser_platform": "Win32",
            "browser_version": self.BROWSER_VERSION, # '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            "channel": "tiktok_web",
            "cookie_enabled": "true",
            "data_collection_enabled": "true",
            "device_id": self.device_id,
            "device_platform": "web_pc",
            "focus_state": "true",
            "from_page": "user",
            # "history_len": "16",
            "is_fullscreen": "false",
            "is_page_visible": "true",
            "odinId": self.user_id,
            "os": "windows",
            "priority_region": "US",
            "referer": f"https://www.tiktok.com/@{self.unique_id}",
            "region": "US",
            "screen_height": "1080",
            "screen_width": "1920",
            "tz_name": self.tz_name,
            "user_is_login": "true",
            "webcast_language": "en",
            "msToken": self.session.cookies.get_dict().get("msToken")
        }
        data = {}
        if avatar_uri:
            data["avatar_uri"] = avatar_uri # 头像上传后的链接地址
        if new_nickname:
            data["nickname"] = new_nickname.replace(" ", "")
        if signature_text:
            data["signature"] = signature_text.replace(" ", "")
        if not data:
            return {"status": 0, "data": {"text": f"{self.unique_id} 未提供需要修改的个人信息", "response_data": ""}, "login_expired": 0}
        
        data["tt_csrf_token"] = self.session.cookies.get_dict().get("tt_csrf_token")
        params, data = group_XBogus_XGnarly(
            params=params,
            data=data,
            no_json=True, 
            ua=self.ua,
            now_time=now_time,
            canvas_num=self.canvas_num, 
            extra_num=0,
            page_sign=self.page_sign
        )
        try:
            response: "Response" = self.do_req(method="POST", url=self.join_url_params(url, params=params), headers=headers, data=data)
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 修改个人信息接口请求出错：{e}", "response_data": ""}, "login_expired": 0}

        try:
            response_content = response.content
            if response_content:
                response_json: dict = response.json()
                now_user_id = response_json.get("user", {}).get("uid", "")
                now_nickname = response_json.get("user", {}).get("nickname", "")
                user_signature = response_json.get("user", {}).get("signature", "")
                all_profile_pic = {
                    "avatar_larger": response_json.get("user", {}).get("avatar_larger", {}),
                    "avatar_thumb": response_json.get("user", {}).get("avatar_thumb", {}),
                    "avatar_medium": response_json.get("user", {}).get("avatar_medium", {})
                }
                if response_json.get("status_msg").lower() == "login expired":
                    return {"status": 0, "data": {"text": f"{self.unique_id} 修改个人信息失败，登录失效", "response_data": response_json}, "login_expired": 1}
                elif response_json.get("status_msg").lower() == "slow down, you are editing too fast.":
                    return {"status": 0, "data": {"text": f"{self.unique_id} 修改个人信息失败，账号已被强制掉线", "response_data": response_json}, "login_expired": 1}
                elif now_user_id:
                    return {
                        "status": 1, 
                        "data": {"text": f"{self.unique_id} 修改个人信息成功，现昵称：{now_nickname}，个性签名：{user_signature}", "response_data": response_json}, 
                        "now_nickname": now_nickname,
                        "user_signature": user_signature,
                        "all_profile_pic": all_profile_pic,
                        "login_expired": 0
                    }
                return {"status": 0, "data": {"text": f"{self.unique_id} 修改个人信息失败", "response_data": response_json}, "login_expired": 0}
            return {"status": 0, "data": {"text": f'{self.unique_id} 修改个人信息失败', "response_data": ""}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 修改信息接口响应内容解析出错：{e}", "response_data": ""}, "login_expired": 0}
 
    # 私密账号 公开账号 的设置（Duet、Stitch、download）
    def set_private_account(self, setting_type=1):
        fill_text = "私密账号" if (setting_type == 1) else "公开账号"
        headers = {
            "User-Agent": self.ua,
            "Content-Type": "application/x-www-form-urlencoded",
            "accept-language": "en-US,en;q=0.9",
            "cache-control": "no-cache",
            "origin": "https://www.tiktok.com",
            "pragma": "no-cache",
            "priority": "u=1, i",
            "referer": "https://www.tiktok.com/setting",
            "sec-ch-ua-mobile": "?0",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "tt-csrf-token": self.session.cookies.get_dict().get("tt_csrf_token"),
        }
        params = {
            "WebIdLastTime": self.WebIdLastTime,
            "aid": "1988",
            "app_language": "en",
            "app_name": "tiktok_web",
            "browser_language": "en-US",
            "browser_name": self.ua.split("/")[0], # Mozilla,
            "browser_online": "true",
            "browser_platform": "Win32",
            "browser_version": self.BROWSER_VERSION, # '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            "channel": "tiktok_web",
            "cookie_enabled": "true",
            "data_collection_enabled": "true",
            "device_id": self.device_id,
            "device_platform": "web_pc",
            "field": "private_account",
            "focus_state": "true",
            "from_page": "setting",
            # "history_len": "5",
            "is_fullscreen": "false",
            "is_page_visible": "true",
            "odinId": self.user_id,
            "os": "windows",
            "priority_region": "US",
            "referer": "",
            "region": "US",
            "screen_height": "1080",
            "screen_width": "1920",
            "tt_csrf_token": self.session.cookies.get_dict().get("tt_csrf_token"),
            "tz_name": self.tz_name,
            "user_is_login": "true",
            "value": str(setting_type),
            "webcast_language": "en"
        }
        try:
            response: "Response" = self.do_req(method="POST", url=self.join_url_params("https://www.tiktok.com/api/privacy/user/private_account/update/v1", params), headers=headers)
        except Exception as e:
            return {"status": 0, "data": {"text": f'{self.unique_id} 设置 {fill_text} 请求出错：{e}', "response_data": ""}, "login_expired": 0}
        
        try:
            response_content = response.content
            if response_content:
                response_json: dict = response.json()
                if response_json.get("status_msg").lower() == "login expired":
                    return {"status": 0, "data": {"text": f'{self.unique_id} 设置 {fill_text} 失败，登录失效', "response_data": response_json}, "login_expired": 1}
                elif response_json.get("status_msg"):
                    return {"status": 0, "data": {"text": f'{self.unique_id} 设置 {fill_text} 失败', "response_data": response_json}, "login_expired": 0}
                return {"status": 1, "data": {"text": f'{self.unique_id} 设置 {fill_text} 成功', "response_data": response_json}, "login_expired": 0}
            return {"status": 0, "data": {"text": f'{self.unique_id} 设置 {fill_text} 失败', "response_data": ""}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f'{self.unique_id} 设置 {fill_text} 响应内容解析出错：{e}', "response_data": ""}, "login_expired": 0}