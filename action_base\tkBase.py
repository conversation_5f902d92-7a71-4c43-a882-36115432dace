# 基础类，只存放所有接口
import requests
import logging

from utils import *
from utils.shared_manager import get_manager_namespace


def random_US_tz():
    # tz_name：时区
    return random.choice([
        "America/Los_Angeles",  # 旧金山(San Francisco) (太平洋时间,PST/PDT)
        "Pacific/Honolulu",  # 夏威夷(Hawaii) (夏威夷-阿留申时间，HST)
        "America/New_York",  # 纽约(New York) (东部时间,EST/EDT)
        "America/Chicago",  # 芝加哥(Chicago) (中部时间,CST/CDT)
        "America/Denver",  # 丹佛(Denver) (山地时间,MST/MDT)
        "America/Phoenix",  # 凤凰城(Phoenix, Arizona) (常年 MST,没有夏令时)
    ])


class TkBase(ReqBase):
    def __init__(self,
        config=None,
        unique_id=None, object_id=None,  # 两者传其一
        cookies_dict={}, canvas_num=2010578131,
        user_id="", user_sec_uid="", device_id="", WebIdLastTime="",  # 这 4 入参为空，实例化时手动调用 sync_init 获取
        tz_name="", proxies=None
    ):
        super().__init__(config=config, cookies_dict=cookies_dict, proxies=proxies, max_fail_count=5)
        
        self.logger = logging.getLogger(config.get("task_type", "unknown_task"))
        if not self.logger.handlers:
            self.logger.propagate = True

        self.config = config
        self.BROWSER_VERSION = "/".join(self.ua.split("/")[1:])

        # 确保 msToken 存在于会话中，如果不存在，则从共享对象或配置中获取
        if "msToken" not in self.session.cookies:
            shared_ns = get_manager_namespace()
            if shared_ns and hasattr(shared_ns, 'default_ms_token'):
                ms_token = shared_ns.default_ms_token
            else:
                ms_token = config["default_ms_token"]

            if ms_token:
                self.session.cookies.set("msToken", ms_token, domain=".tiktok.com")

        self.app_session = requests.Session()
        # 通用信息 ——————————————————————————————————————————————————————————————————————————————————————————————————————————————
        self.device_id = device_id
        self.unique_id = unique_id or object_id
        self.user_id = user_id
        self.user_sec_uid = user_sec_uid
        self.WebIdLastTime = WebIdLastTime
        self.canvas_num = canvas_num
        self.page_sign = 0
        self.tz_name = tz_name if tz_name else random_US_tz()

    def sync_init(self):
        if (not self.device_id) or (not self.WebIdLastTime) or (not self.user_id) or (not self.user_sec_uid):
            for i in range(5):
                get_index_page_result = self.get_index_page()
                print(get_index_page_result)
                if get_index_page_result.get("status"):
                    self.page_sign = get_page_sign()
                    return get_index_page_result
            return get_index_page_result

    # 首页个人 csrfToken
    # /@unique_id 首页个人信息（可用于查询其他用户信息，查询其他用户信息后，执行自己账号相关操作前需要自行调用这个接口重置正确的 csrfToken）
    def get_index_page(self, search_unique_id=""):
        inner_unique_id = search_unique_id if search_unique_id else self.unique_id
        headers = {
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "accept-language": "en-US,en;q=0.9",
            "cache-control": "no-cache",
            "pragma": "no-cache",
            "priority": "u=0, i",
            "sec-ch-ua-mobile": "?0",
            "sec-fetch-dest": "document",
            "sec-fetch-mode": "navigate",
            "sec-fetch-site": "none",
            "sec-fetch-user": "?1",
            "upgrade-insecure-requests": "1",
            "user-agent": self.ua
        }
        try:
            response: requests.Response = self.do_req(method="GET", url=f"https://www.tiktok.com/@{inner_unique_id}",
                                                      headers=headers)
        except Exception as e:
            return {"status": 0,
                    "data": {"text": f'{self.unique_id} 请求首页获取 csrfToken 出错：{e}', "response_data": ""},
                    "login_expired": 0}

        try:
            response_text = response.text
            default_scope = extract_nested_objects(text=response_text, key="__DEFAULT_SCOPE__")
            if default_scope:
                webapp_app_context = default_scope.get("webapp.app-context", {})
                webapp_user_detail = default_scope.get("webapp.user-detail", {})

            csrfToken = webapp_app_context.get("csrfToken", "")
            if not csrfToken:
                return {"status": 0,
                        "data": {"text": f'{self.unique_id}请求用户 {inner_unique_id} 首页获取 csrfToken 失败',
                                 "response_data": csrfToken}, "login_expired": 0}

            if not isinstance(webapp_app_context, dict):
                return {"status": 0,
                        "data": {"text": f'{self.unique_id}请求用户 {inner_unique_id} 首页获取 webapp_app_context 失败',
                                 "response_data": webapp_app_context}, "login_expired": 0}

            if not isinstance(webapp_user_detail, dict):
                return {"status": 0,
                        "data": {"text": f'{self.unique_id}请求用户 {inner_unique_id} 首页获取 webapp_user_detail 失败',
                                 "response_data": webapp_user_detail}, "login_expired": 0}

            userInfo = webapp_user_detail.get("userInfo", {}).get("user", {})
            if not userInfo:
                return {"status": 0, "data": {
                    "text": f'{self.unique_id}请求用户 {inner_unique_id} 首页获取用户详情响应内容解析失败，无此用户',
                    "response_data": userInfo}, "login_expired": 0}

            user_id = userInfo.get("id", "")
            user_sec_uid = userInfo.get("secUid", "")
            device_id = webapp_app_context.get("wid", "")
            WebIdLastTime = webapp_app_context.get("webIdCreatedTime", "")
            if not search_unique_id:
                self.user_id = user_id
                self.user_sec_uid = user_sec_uid
                self.device_id = device_id
                self.WebIdLastTime = WebIdLastTime
                self.update_session_cookies({"tt_csrf_token": csrfToken})
            user_signature = userInfo.get("signature", "")
            shareMeta = webapp_user_detail.get("shareMeta", "")
            print(
                f'{"—" * 200}\n' +
                f"用户信息：{shareMeta}\n" +
                f"uid：{user_id if search_unique_id else self.user_id}\n" +
                f"sec_uid：{user_sec_uid if search_unique_id else self.user_sec_uid}\n" +
                f"当前用户签名：{user_signature}\n" +
                "—" * 200
            )
            return {"status": 1, "data": {"text": f'{self.unique_id}请求用户 {inner_unique_id} 首页获取 csrfToken 成功',
                                          "response_data": {
                                              "csrfToken": csrfToken,
                                              "user_infor": shareMeta,
                                              "username": search_unique_id if search_unique_id else self.unique_id,
                                              "user_id": user_id if search_unique_id else self.user_id,
                                              "sec_uid": user_sec_uid if search_unique_id else self.user_sec_uid,
                                              "signature_text": user_signature
                                          }}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {
                "text": f'{self.unique_id}请求用户 {inner_unique_id} 首页获取 csrfToken 与用户详情流程出错：{e}',
                "response_data": ""}, "login_expired": 0}

    # 搜索指定用户（web），需要 cookie 的键：sid_guard、msToken、ttwid
    def search_user(self, search_keyword, cursor=0, search_id=""):
        now_time = round(time.time() * 1000)
        url = "https://www.tiktok.com/api/search/user/full/"
        referer_params = {
            "q": search_keyword,
            "t": now_time,
        }
        location_href = self.join_url_params("https://www.tiktok.com/search/user", referer_params)
        headers = {
            "accept": "*/*",
            "accept-language": "en-US,en;q=0.9",
            "cache-control": "no-cache",
            "pragma": "no-cache",
            "priority": "u=1, i",
            "referer": location_href,
            "sec-ch-ua-mobile": "?0",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": self.ua
        }
        params = {
            "WebIdLastTime": self.WebIdLastTime,  # 应该是登录后的标记时间，一直固定
            "aid": "1988",
            "app_language": "en",
            "app_name": "tiktok_web",
            "browser_language": "en-US",
            "browser_name": self.ua.split("/")[0],  # Mozilla,
            "browser_online": "true",
            "browser_platform": "Win32",
            "browser_version": self.BROWSER_VERSION,
            # '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
            "channel": "tiktok_web",
            "cookie_enabled": "true",
            "cursor": str(cursor * 10),  # 需要的起始位置，类似与 第几页×每页条数，浏览器搜索为 10 条，实际是 2 个请求
            "data_collection_enabled": "true",
            "device_id": self.device_id,
            "device_platform": "web_pc",
            "focus_state": "true",
            "from_page": "search",
            # "history_len": "4",
            "is_fullscreen": "false",
            "is_page_visible": "true",
            "keyword": search_keyword,
            "odinId": self.user_id,
            "os": "windows",
            "priority_region": "US",
            "referer": "",
            "region": "US",
            "screen_height": "1080",
            "screen_width": "1920",
            "tz_name": self.tz_name,
            "user_is_login": "true",
            "web_search_code": json.dumps({
                "tiktok": {
                    "client_params_x": {
                        "search_engine": {
                            "ies_mt_user_live_video_card_use_libra": 1,
                            "mt_search_general_user_live_card": 1
                        }
                    },
                    "search_server": {}
                }
            }, separators=(",", ":")),
            "webcast_language": "en",
            "msToken": self.session.cookies.get_dict().get("msToken")
        }
        if search_id:
            params["search_id"] = search_id

        params, data = group_XBogus_XGnarly(
            params=params,
            data="",
            no_json=True,
            ua=self.ua,
            now_time=int(now_time),  # 13 位
            canvas_num=self.canvas_num,
            extra_num=0,
            page_sign=self.page_sign
        )
        try:
            response: requests.Response = self.do_req(method="GET", url=self.join_url_params(url, params),
                                                      headers=headers)
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 搜索用户请求出错：{e}", "response_data": ""},
                    "login_expired": 0}

        try:
            response_json = response.json()
            search_users_list = response_json.get("user_list")
            if isinstance(search_users_list, list) and search_users_list:
                search_users_dict = {
                    "search_id": response_json.get("rid", ""),  # 用于翻页
                    "search_users_list": search_users_list,
                }
                return {"status": 1, "data": {"text": f"{self.unique_id} 搜索用户成功：{search_keyword}",
                                              "response_data": search_users_dict}, "login_expired": 0}
            return {"status": 0,
                    "data": {"text": f"{self.unique_id} 搜索用户失败：{search_keyword}", "response_data": response_json},
                    "login_expired": 0}
        except Exception as e:
            return {"status": 0,
                    "data": {"text": f"{self.unique_id} 搜索用户响应内容解析出错：{e}", "response_data": ""},
                    "login_expired": 0}

    # 自动补充用户信息 user_id、sec_uid
    def fill_follow_user_info(self, follow_user_info_dict, max_search_page=1, error_text=""):
        try:
            if not follow_user_info_dict.get("username"):
                return {"status": 0, "data": {"text": f'{self.unique_id}  {error_text}', "response_data": ""},
                        "login_expired": 0}

            if (follow_user_info_dict.get("user_id")) and (follow_user_info_dict.get("sec_uid")):
                return follow_user_info_dict

            # """
            # 方案1，根据用户名在搜索里面获取
            get_index_page_result = self.get_index_page(search_unique_id=follow_user_info_dict.get("username"))
            if not get_index_page_result.get("status"):
                return {"status": 0, "data": {"text": f"{self.unique_id} 未提供完整的用户信息（user_id、sec_uid）",
                                              "response_data": get_index_page_result.get("data", {})},
                        "login_expired": 0}

            user_info = get_index_page_result.get("data", {}).get("response_data", {})
            if user_info.get("user_id") and user_info.get("sec_uid"):
                print(f"未提供完整的用户信息（user_id、sec_uid），自动填充成功：")
                print(json.dumps(user_info, indent=4, ensure_ascii=False))
                return {"status": 1, "data": {"text": f"{self.unique_id} 自动补充完整的用户信息（user_id、sec_uid）成功",
                                              "response_data": user_info}, "login_expired": 0}
            return {"status": 0, "data": {"text": f"{self.unique_id} 未提供完整的用户信息（user_id、sec_uid）",
                                          "response_data": get_index_page_result.get("data", {})}, "login_expired": 0}
            # """

            """ 
            # 方案2，根据用户名在搜索里面获取（有时忽然搜不到是因为需要过验证码，不稳定所以弃用此方案）
            search_id = ""
            for i in range(max_search_page):
                search_user_result = self.search_user(search_keyword=follow_user_info_dict.get("username"), cursor=i, search_id=search_id)
                if not search_user_result.get("status"):
                    return search_user_result
                
                search_users_list = search_user_result.get("data", {}).get("response_data", {}).get("search_users_list")
                search_id = search_user_result.get("data", {}).get("response_data", {}).get("search_id")
                parse_search_user_result = self.parse_search_user(search_users_list, search_keyword=follow_user_info_dict.get("username"), follow_user_info_dict=follow_user_info_dict)
                if ("关注" in error_text) and (parse_search_user_result.get("is_follow")):
                    return {"status": 0, "data": {"text": f"{self.unique_id} 该用户已关注", "response_data": ""}, "login_expired": 0}
                if not search_id:
                    return {"status": 0, "data": {"text": f"{self.unique_id} 未获取到 search_id，没有更多的该用户", "response_data": search_user_result}, "login_expired": 0}

                if parse_search_user_result.get("user_id") and parse_search_user_result.get("sec_uid"):
                    return {"status": 1, "data": {"text": f"{self.unique_id} 自动补充完整的用户信息（user_id、sec_uid）成功", "response_data": parse_search_user_result}, "login_expired": 0}
            else:
                return {"status": 0, "data": {"text": f"{self.unique_id} 未提供完整的用户信息（user_id、sec_uid）", "response_data": ""}, "login_expired": 0}
            """
        except Exception as e:
            return {"status": 0,
                    "data": {"text": f"{self.unique_id} 未提供完整的用户信息（user_id、sec_uid），自动补充信息出错：{e}",
                             "response_data": ""}, "login_expired": 0}
