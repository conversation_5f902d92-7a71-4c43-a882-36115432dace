import json, time, uuid, random, blackboxprotobuf
from action_base.tkBase import TkBase
from utils import *
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from requests import Response

class ChatBase(TkBase):
    # 原始 url 生成第一次的 share_url
    def create_share_url(self, origin_url=""):
        url = "https://www.tiktok.com/link/"
        params = {
            "aid": "1233",
            "share_link_id": str(uuid.uuid4()),
            "social_share_type": "4",
            "target": origin_url,
        }
        share_url = f"{url}?{urlencode(params)}"
        return share_url

    # 获取短链
    def get_shorten_url(self, share_url=""):
        headers = {
            "User-Agent": "com.zhiliaoapp.musically/2023907030 (Linux; U; Android 13; zh_CN; M2104K10AC; Build/TP1A.220624.014; Cronet/TTNetVersion:a482972f 2025-04-03 QuicVersion:52c2b40d 2025-04-03)",
            "sdk-version": "2",
            "passport-sdk-version": "-1",
            "oec-vc-sdk-version": "3.0.8.i18n",
            "x-vc-bdturing-sdk-version": "2.3.10.i18n",
            "x-tt-request-tag": "n=0;nr=111;bg=0",
            "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
            "x-tt-store-region": "us",
            "x-tt-store-region-src": "uid",
            "rpc-persist-pyxis-policy-v-tnc": "1",
            "rpc-persist-pyxis-policy-state-law-is-ca": "0",
            "x-ss-dp": "1233",
        }
        params = {
            "device_platform": "android",
            "os": "android",
            "ssmix": "a",
            "channel": "googleplay",
            "aid": "1233",
            "app_name": "musical_ly",
            "version_code": "390703",
            "version_name": "39.7.3",
            "manifest_version_code": "2023907030",
            "update_version_code": "2023907030",
            "ab_version": "39.7.3",
            "resolution": "1080*2260",
            "dpi": "440",
            "device_type": "M2104K10AC",
            "device_brand": "Redmi",
            "language": "zh-Hans",
            "os_api": "33",
            "os_version": "13",
            "ac": "wifi",
            "is_pad": "0",
            "current_region": "TW",
            "app_type": "normal",
            "sys_region": "CN",
            "timezone_name": "Asia/Yerevan",
            "residence": "TW",
            "app_language": "en",
            "timezone_offset": "14400",
            "host_abi": "arm64-v8a",
            "locale": "en",
            "ac2": "wifi5g",
            "uoo": "0",
            "op_region": "TW",
            "build_number": "39.7.3",
            "region": "CN",
        }
        data = {
            "scene": "35",
            "platform_id": "copy_link",
            "share_url": share_url
        }
        try:
            response: "Response" = self.do_req(session=self.app_session, method="POST", url=self.join_url_params("https://api16-normal-useast8.tiktokv.us/tiktok/share/link/shorten/v1/", params), headers=headers, data=data)
        except Exception as e:
            return {"status": 0, "data": {"text": f'{self.unique_id} 创建 {share_url} 的短链请求出错：{e}', "response_data": ""}, "login_expired": 0}
        
        try:
            response_json: dict = response.json()
            shorten_url = response_json.get("shorten_url", "")
            status_msg = response_json.get("status_msg", "")
            if (not status_msg) and shorten_url:
                return {"status": 1, "data": {"text": f"{self.unique_id} 创建 {share_url} 的短链成功", "response_data": shorten_url}, "login_expired": 0}
            return {"status": 0, "data": {"text": f"{self.unique_id} 创建 {share_url} 的短链失败", "response_data": response_json}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f'{self.unique_id} 创建 {share_url} 的短链响应内容解析出错：{e}', "response_data": ""}, "login_expired": 0}

    # 通过视频 id 获取视频信息
    def get_video_info(self, video_id=0):
        url = "https://www.tiktok.com/api/item/detail/"
        now_time = int(time.time())
        headers = {
            "User-Agent": self.ua,
            "sec-ch-ua-mobile": "?0",
            "origin": "https://www.tiktok.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://www.tiktok.com/",
            "accept-language": "en-US,en;q=0.9",
            "priority": "u=1, i"
        }
        params = {
            "WebIdLastTime": self.WebIdLastTime,
            "aid": "1988",
            "app_language": "en",
            "app_name": "tiktok_web",
            "browser_language": "en-US",
            "browser_name": self.ua.split("/")[0], # Mozilla,
            "browser_online": "true",
            "browser_platform": "Win32",
            "browser_version": self.BROWSER_VERSION, # '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
            "channel": "tiktok_web",
            "cookie_enabled": "true",
            "device_id": self.device_id,
            "device_platform": "web_pc",
            "focus_state": "true",
            "from_page": "user",
            # "history_len": "4",
            "is_fullscreen": "false",
            "is_page_visible": "true",
            "language": "en",
            "os": "windows",
            "priority_region": "US",
            "referer": "",
            "region": "US",
            "root_referer": "https://www.tiktok.com/",
            "screen_height": "1080",
            "screen_width": "1920",
            "webcast_language": "en",
            "itemId": video_id
        }
        X_Bogus = get_XBogus(
            params=params,
            data="",
            ua=self.ua,
            now_time=now_time,
            canvas_num=self.canvas_num,
            extra_num=0
        )
        params["X-Bogus"] = X_Bogus
        try:
            response: "Response" = self.do_req(method="GET", url=self.join_url_params(url, params), headers=headers)
        except Exception as e:
            return {"status": 0, "data": {"text": f'{self.unique_id} 获取视频 {video_id} 详情请求出错：{e}', "response_data": ""}, "login_expired": 0}
        
        try:
            response_json: dict = response.json()
            itemInfo = response_json.get("itemInfo", {})
            itemStruct = itemInfo.get("itemStruct", {}) if itemInfo else {}
            if itemStruct:
                author = itemStruct.get("author", {})
                video = itemStruct.get("video", {})
                video_info = {
                    "id": video_id,
                    "author": {
                        "uniqueId": author["uniqueId"],
                        "nickname": author["nickname"],
                        "id": author["id"],
                        "secUid": author["secUid"],
                        "avatarThumb": author["avatarThumb"] # 末尾会多个2
                    },
                    "video": {
                        "cover": video["cover"],
                        "width": video["width"],
                        "height": video["height"],
                    }
                }
                return {"status": 1, "data": {"text": f'{self.unique_id} 获取视频 {video_id} 详情成功', "response_data": video_info}, "login_expired": 0}
            return {"status": 0, "data": {"text": f'{self.unique_id} 获取视频 {video_id} 详情失败', "response_data": response_json}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f'{self.unique_id} 获取视频 {video_id} 详情响应内容解析出错：{e}', "response_data": ""}, "login_expired": 0}

    # 获取 chat_id（信息 web 与 app 通用）
    def get_chat_id(self, receiver_username="", receiver_userid=""):
        now_time = int(time.time())
        url = "https://im-api.tiktok.com/v2/conversation/create"
        headers = {
            "User-Agent": self.ua,
            "Accept": "application/x-protobuf",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "content-type": "application/x-protobuf",
            "sec-ch-ua-mobile": "?0",
            "origin": "https://www.tiktok.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://www.tiktok.com/",
            "accept-language": "en-US,en;q=0.9",
            "priority": "u=1, i"
        }
        params = {
            "msToken": self.session.cookies.get_dict().get("msToken"),
        }
        data = {
            "1": 609,
            "2": 10008,
            "3": "1.2.9",
            "4": {},
            "5": 3,
            "6": 0,
            "7": "",
            # "7": "1d04cd6:Detached: 1d04cd64a8951018288519972e6270738f23c23f", # 写死的，可能类似版本号，不带也可以
            "8": {
                "609": {
                    "1": 1,
                    "2": [int(self.user_id), int(receiver_userid)]
                }
            },
            "9": self.device_id,
            "11": "web",
            "15": [
                {"1": "aid", "2": "1988"},
                {"1": "app_name","2": "tiktok_web"},
                {"1": "channel","2": "web"},
                {"1": "device_platform", "2": "web_pc"},
                {"1": "device_id", "2": self.device_id},
                {"1": "region", "2": "US"},
                {"1": "priority_region", "2": "US"},
                {"1": "os", "2": "windows"},
                {"1": "referer", "2": f"https://www.tiktok.com/@{receiver_username}" if receiver_username else "https://www.tiktok.com"},
                {"1": "root_referer", "2": ""},
                {"1": "cookie_enabled", "2": "true"},
                {"1": "screen_width", "2": "1920"},
                {"1": "screen_height", "2": "1080"},
                {"1": "browser_language", "2": "en-US"},
                {"1": "browser_platform", "2": "Win32"},
                {"1": "browser_name", "2": self.ua.split("/")[0]}, # Mozilla
                {"1": "browser_version", "2": self.BROWSER_VERSION}, # '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
                {"1": "browser_online", "2": "true"},
                {"1": "verifyFp", "2": ""},
                {"1": "app_language", "2": "en"},
                {"1": "webcast_language", "2": "en"},
                {"1": "tz_name", "2": self.tz_name},
                {"1": "is_page_visible", "2": "true"},
                {"1": "focus_state", "2": "false"},
                {"1": "is_fullscreen", "2": "false"},
                {"1": "history_len", "2": "4"},
                {"1": "user_is_login", "2": "true"},
                {"1": "data_collection_enabled", "2": "true"},
                {"1": "from_appID", "2": "1988"},
                {"1": "locale", "2": "en"},
                {"1": "user_agent", "2": self.ua},
                {"1": "Web-Sdk-Ms-Token", "2": self.session.cookies.get_dict().get("msToken")} # 不带也可以
            ],
            "18": 1
        }
        typedef = {'1': {'type': 'int'}, '2': {'type': 'int'}, '3': {'type': 'string'}, '4': {'message_typedef': {}, 'type': 'message'}, '5': {'type': 'int'}, '6': {'type': 'int'}, '7': {'type': 'string'}, '8': {'field_order': ['609'], 'message_typedef': {'609': {'field_order': ['1', '2', '2'], 'message_typedef': {'1': {'type': 'int'}, '2': {'seen_repeated': True, 'type': 'int'}}, 'type': 'message'}}, 'type': 'message'}, '9': {'type': 'string'}, '11': {'type': 'string'}, '15': {'seen_repeated': True, 'field_order': ['1', '2'], 'message_typedef': {'1': {'type': 'string'}, '2': {'type': 'string'}}, 'type': 'message'}, '18': {'type': 'int'}}
        try:
            data = blackboxprotobuf.encode_message(data, typedef)
        except Exception as e:
            return {"status": 0, "data": {"text": f'{self.unique_id} 创建聊天 data 编码出错：{e}', "response_data": [data, typedef]}, "login_expired": 0}
        
        X_Bogus = get_XBogus(
            params=params,
            data=data,
            ua=self.ua, 
            now_time=now_time,
            canvas_num=self.canvas_num,
            extra_num=0
        )
        params["X-Bogus"] = X_Bogus
        try:
            response: "Response" = self.do_req(method="POST", url=self.join_url_params(url, params), headers=headers, data=data)
        except Exception as e:
            return {"status": 0, "data": {"text": f'{self.unique_id} 创建聊天出错：{e}', "response_data": ""}, "login_expired": 0}
        
        try:
            response_content = response.content
            response_json = blackboxprotobuf.decode_message(response_content)[0]
            chat_data: dict = response_json.get("6", {}).get("609", {}).get("1", {})
            if (response_json.get("4") == "OK") and chat_data:
                chat_dict = {
                    "chat_info": chat_data.get("1"),
                    "chat_id": chat_data.get("2")
                }
                return {"status": 1, "data": {"text": f"{self.unique_id} 创建与用户 {receiver_username} 的聊天成功", "response_data": chat_dict}, "login_expired": 0}
            return {"status": 0, "data": {"text": f"{self.unique_id} 创建与用户 {receiver_username} 的聊天失败", "response_data": response_json}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f'{self.unique_id} 创建聊天响应内容解析出错：{e}', "response_data": ""}, "login_expired": 0}

    # 解析 web/app私信响应的结果(/send 接口)
    def parse_chat_result(self, response_content=b'', message_text="", chat_id=0):
        try:
            response_json = blackboxprotobuf.decode_message(response_content)[0]
            error_message = response_json.get("6", {}).get("100", {}).get("6", "")
            if error_message:
                json_error_message = json.loads(error_message)
                error_tips = (json_error_message.get("status_msg",{}).get("msg_content", {}).get("tips", "") if (json_error_message.get("status_msg",{}).get("msg_content", {}) and isinstance(json_error_message.get("status_msg",{}).get("msg_content", {}), dict)) else json_error_message.get("status_msg", {})) if json_error_message.get("status_msg",{}) else json_error_message
                if error_tips in ["The message couldn’t be sent due to receiver’s settings. We will resend the message once they update their permission.", "由于接收方的设置，无法发送消息。更新权限后，我们将重新发送消息。"]:
                    return {"status": 2, "data": {"text": f"{self.unique_id} 发送私信失败：{message_text} (该用户暂未放开聊天限制)" if message_text else f"{self.unique_id} 发送私信失败，该用户暂未放开聊天限制", "response_data": error_tips}, "login_expired": 0, "chat_id": chat_id}
                elif error_tips in ["Only friends can send messages to each other", "只有好友才能互发消息"]:
                    return {"status": 2, "data": {"text": f"{self.unique_id} 发送私信失败：{message_text} (只有好友关系才能互发消息)" if message_text else f"{self.unique_id} 发送私信失败，只有好友关系才能互发消息", "response_data": error_tips}, "login_expired": 0, "chat_id": chat_id}
                elif ("This message may be in violation of our Community Guidelines" in error_tips) or ("保护社区安全" in error_tips):
                    return {"status": 2, "data": {"text": f"{self.unique_id} 发送私信失败：{message_text} (该消息受 tk 限制)" if message_text else f"{self.unique_id} 发送私信失败，该消息受 tk 限制", "response_data": error_tips}, "login_expired": 0, "chat_id": chat_id}
                elif error_tips in [
                    "You can only send up to 1 message before this user accepts your message request.", 
                    "在此用户接受你的消息请求之前，你最多只能发送 1 条消息。",
                    "You can send up to 3 messages until this user accepts your message request.",
                    "在此用户接受你的消息请求之前，你最多只能发送 3 条消息。"
                ]:
                    return {"status": 1, "data": {"text": f"{self.unique_id} 发送私信成功：{message_text} ，该用户私信存在上限" if message_text else f"{self.unique_id} 发送私信成功，该用户私信存在上限", "response_data": error_tips}, "login_expired": 0, "chat_id": chat_id}
                elif error_tips in [
                    "Chat messages limit reached. You will not be able to send messages to this user."
                    "聊天消息条数已达上限，你将无法向该用户发送消息。"
                ]:
                    return {"status": 2, "data": {"text": f"{self.unique_id} 发送私信失败：{message_text} ，该用户私信上限" if message_text else f"{self.unique_id} 发送私信失败，该用户私信上限", "response_data": error_tips}, "login_expired": 0, "chat_id": chat_id}
                elif error_tips == {"msg_type": 0, "msg_content": {}, "notice_code": "chat_request_sent"}:
                    return {"status": 1, "data": {"text": f"{self.unique_id} 发送私信成功：{message_text}" if message_text else f"{self.unique_id} 发送私信成功", "response_data": response_json}, "login_expired": 0, "chat_id": chat_id}
                return {"status": 2, "data": {"text": f"{self.unique_id} 发送私信：{message_text}" if message_text else f"{self.unique_id} 发送私信未知结果", "response_data": response_json}, "login_expired": 0, "chat_id": chat_id}
            elif response_json.get("4") == "OK":
                return {"status": 1, "data": {"text": f"{self.unique_id} 发送私信成功：{message_text}" if message_text else f"{self.unique_id} 发送私信成功", "response_data": response_json}, "login_expired": 0, "chat_id": chat_id}
            return {"status": 0, "data": {"text": f"{self.unique_id} 发送私信失败：{message_text}" if message_text else f"{self.unique_id} 发送私信失败", "response_data": response_json}, "login_expired": 0, "chat_id": chat_id}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 发送私信响应内容解析出错：{e}", "response_data": ""}, "login_expired": 0, "chat_id": chat_id}
        
    # 发送私信（app），X-Tt-Token 是 app 发送方的身份标识，params 与 4神 可以不带，data 携带必要的参数，chat_info 与 chat_id 只能标识双方，无法标识发送方
    def app_message_send(self, message_text="", message_data=b'', chat_info="", chat_id=0, X_TT_Token=""):
        if not X_TT_Token:
            return {"status": 0, "data": {"text": f'{self.unique_id} 发送私信为提供 X-Tt-Token', "response_data": X_TT_Token}, "login_expired": 0, "chat_id": chat_id}

        tt_target_idc = self.app_session.cookies.get("tt-target-idc", self.session.cookies.get("tt-target-idc"), "")
        if tt_target_idc == "useast8":
            url = "https://api16-normal-useast8.tiktokv.us/v1/message/send"
        elif tt_target_idc == "alisg":
            url = "https://api22-normal-c-alisg.tiktokv.com/v1/message/send"
        else:
            return {"status": 0, "data": {"text": f'{self.unique_id} 发送私信未知 cookie -> tt-target-idc 类型', "response_data": tt_target_idc}, "login_expired": 0, "chat_id": chat_id}

        iid = f'750448245{"".join(random.choices("0123456789", k=10))}'
        headers = {
            "User-Agent": "com.zhiliaoapp.musically/2023701040 (Linux; U; Android 13; en; M2104K10AC; Build/TP1A.220624.014;tt-ok/3.12.13.4-tiktok)",
            "Connection": "Keep-Alive",
            "Locale": "en",
            "x-tt-pba-enable": "1",
            "X-Biz-Id": "1180",
            "x-metasec-event-source": "native",
            "x-bd-kmsv": "0",
            "x-tt-dm-status": "login=1;ct=1;rt=8",
            "sdk-version": "2",
            "Content-Type": "application/x-protobuf",
            "X-Tt-Token": X_TT_Token
        }
        if not message_data:
            deprecated_uuid = str(uuid.uuid4())
            data = {
                "1": 100,
                "2": 488008,
                "3": "local",
                "4": "", # x-tt-token
                "5": 1,
                "6": 0,
                "7": "0",
                "8": {
                    "100": {
                        '1': chat_info,
                        '2': 1, 
                        '3': chat_id,
                        '4': json.dumps({
                            "text": message_text,
                            "is_card": False,
                            "reference_scene": 0,
                            "sendStartTime": round(time.time() * 1000),
                            "aweType": 700
                        }, separators=(",", ":")), 
                        '6': 7, 
                        '8': deprecated_uuid,
                        '13': {}, 
                        '14': {}
                    }
                },
                '9': self.device_id, 
                # '10': 'googleplay',
                '11': 'android', 
                # '12': 'M2104K10AC', 
                # '13': '13', 
                # '14': '2023701040',
                # '15': [
                #     {'1': 'iid', '2': iid},
                #     {'1': 'aid', '2': '1233'}, 
                #     {'1': 'user-agent', '2': 'okhttp/3.12.13.4-tiktok'}, 
                #     {'1': 'locale', '2': 'en'},
                #     {'1': 'IMSDK-User-ID', '2': self.user_id}
                # ], 
                # '18': 0, 
                # '21': {}
            }
            typedef = {
                '1': {'type': 'int'}, 
                '2': {'type': 'int'}, 
                '3': {'type': 'string'}, 
                '4': {'type': 'string'}, 
                '5': {'type': 'int'}, 
                '6': {'type': 'int'}, 
                '7': {'type': 'string'}, 
                '8': {'field_order': ['100'], 'message_typedef': {'100': {'field_order': ['1', '2', '3', '4', '6', '8', '13', '14'], 'message_typedef': {'1': {'type': 'string'}, '2': {'type': 'int'}, '3': {'type': 'int'}, '4': {'type': 'string'}, '6': {'type': 'int'}, '8': {'type': 'string'}, '13': {'message_typedef': {}, 'type': 'message'}, '14': {'message_typedef': {}, 'type': 'message'}}, 'type': 'message'}}, 'type': 'message'}, 
                '9': {'type': 'string'}, 
                # '10': {'type': 'string'}, 
                '11': {'type': 'string'}, 
                # '12': {'type': 'string'}, 
                # '13': {'type': 'string'}, 
                # '14': {'type': 'string'}, 
                # '15': {'seen_repeated': True, 'field_order': ['1', '2'], 'message_typedef': {'1': {'type': 'string'}, '2': {'type': 'string'}}, 'type': 'message'}, 
                # '18': {'type': 'int'}, 
                # '21': {'message_typedef': {}, 'type': 'message'}
            }
            try:
                data = blackboxprotobuf.encode_message(data, typedef)
            except Exception as e:
                return {"status": 0, "data": {"text": f'{self.unique_id} 发送私信 data 编码出错：{e}', "response_data": [data, typedef]}, "login_expired": 0, "chat_id": chat_id}
        else:
            data = message_data

        try:
            response: "Response" = self.do_req(method="POST", url=url, headers=headers, data=data)
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 发送私信请求出错：{e}", "response_data": ""}, "login_expired": 0, "chat_id": chat_id}
        return self.parse_chat_result(response_content=response.content, message_text=message_text, chat_id=chat_id)

    # 发送私信（web），tt-target-idc 与 tt-target-idc-sign 是 web 发送方的身份标识，chat_info 与 chat_id 只能标识双方，无法标识发送方
    def web_message_send(self, receiver_username="", message_text="", message_data=b'', chat_info="", chat_id=0):
        headers = {
            "User-Agent": self.ua,
            "Accept": "application/x-protobuf",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "sec-ch-ua-mobile": "?0",
            "content-type": "application/x-protobuf",
            "origin": "https://www.tiktok.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://www.tiktok.com/",
            "accept-language": "en-US,en;q=0.9",
            "priority": "u=1, i"
        }
        params = {
            "aid": "1988",
            "version_code": "1.0.0",
            "app_name": "tiktok_web",
            "device_platform": "web_pc",
        }
        if not message_data:
            # 应该是一条消息对应的 uuid，如果同一个 uuid，后续请求可以覆盖掉前面的，直接随机即可
            deprecated_uuid = str(uuid.uuid4())
            data = {
                "1": 100,
                "2": 10011,
                "3": "1.2.9",
                "4": {},
                "5": 3,
                "6": 0,
                "7": "", # 经测试不带也可以
                # "7": "1d04cd6:Detached: 1d04cd64a8951018288519972e6270738f23c23f",
                "8": {
                    "100": {
                        "1": chat_info, # "0:1:发送人user_id:接收user_id"，0,1应该是标记发送和接收
                        "2": 1,
                        "3": int(chat_id), # 事实上即使删除聊天，下次和一样的用户这个 id 也是一样的，所以每次请求 /create 也行
                        "4": json.dumps({"aweType": 0, "text": message_text}, separators=(",", ":")),
                        "5": [
                            {"1": "s:mentioned_users", "2": {}},
                            {"1": "s:client_message_id", "2-1": deprecated_uuid}
                        ],
                        "6": 7,
                        "7": "deprecated",
                        "8": deprecated_uuid
                    }
                },
                "9": self.device_id,
                "11": "web",
                "15": [
                    {"1": "aid", "2": "1988"},
                    {"1": "app_name", "2": "tiktok_web"},
                    {"1": "channel", "2": "web"},
                    {"1": "device_platform", "2": "web_pc"},
                    {"1": "device_id", "2": self.device_id},
                    {"1": "region", "2": "US"},
                    {"1": "priority_region", "2": "US"},
                    {"1": "os", "2": "windows"},
                    {"1": "referer", "2": f"https://www.tiktok.com/@{receiver_username}" if receiver_username else "https://www.tiktok.com/messages?lang=en"},
                    {"1": "root_referer", "2": ""},
                    {"1": "cookie_enabled", "2": "true"},
                    {"1": "screen_width", "2": "1920"},
                    {"1": "screen_height", "2": "1080"},
                    {"1": "browser_language", "2": "en-US"},
                    {"1": "browser_platform", "2": "Win32"},
                    {"1": "browser_name", "2": self.ua.split("/")[0]}, # Mozilla
                    {"1": "browser_version", "2": self.BROWSER_VERSION}, # '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
                    {"1": "browser_online", "2": "true"},
                    {"1": "verifyFp", "2": ""},
                    {"1": "app_language", "2": "en"},
                    {"1": "webcast_language", "2": "en"},
                    {"1": "tz_name", "2": self.tz_name},
                    {"1": "is_page_visible", "2": "true"},
                    {"1": "focus_state", "2": "false"},
                    {"1": "is_fullscreen", "2": "false"},
                    {"1": "history_len", "2": "4"},
                    {"1": "user_is_login", "2": "true"},
                    {"1": "data_collection_enabled", "2": "true"},
                    {"1": "from_appID", "2": "1988"},
                    {"1": "locale", "2": "en"},
                    {"1": "user_agent", "2": self.ua},
                    # {"1": "Web-Sdk-Ms-Token", "2": ""} # 经测试不带也可以
                    {"1": "Web-Sdk-Ms-Token", "2": self.session.cookies.get_dict().get("msToken")}
                ],
                "18": 1
            }
            typedef = {'1': {'type': 'int'}, '2': {'type': 'int'}, '3': {'type': 'string'}, '4': {'message_typedef': {}, 'type': 'message'}, '5': {'type': 'int'}, '6': {'type': 'int'}, '7': {'type': 'string'}, '8': {'field_order': ['100'], 'message_typedef': {'100': {'field_order': ['1', '2', '3', '4', '5', '5', '6', '7', '8'], 'message_typedef': {'1': {'type': 'string'}, '2': {'type': 'int'}, '3': {'type': 'int'}, '4': {'type': 'string'}, '5': {'seen_repeated': True, 'field_order': ['1', '2'], 'message_typedef': {'1': {'type': 'string'}, '2': {'message_typedef': {}, 'type': 'message', 'alt_typedefs': {'1': 'string'}}}, 'type': 'message'}, '6': {'type': 'int'}, '7': {'type': 'string'}, '8': {'type': 'string'}}, 'type': 'message'}}, 'type': 'message'}, '9': {'type': 'string'}, '11': {'type': 'string'}, '15': {'seen_repeated': True, 'field_order': ['1', '2'], 'message_typedef': {'1': {'type': 'string'}, '2': {'type': 'string'}}, 'type': 'message'}, '18': {'type': 'int'}}
            try:
                data = blackboxprotobuf.encode_message(data, typedef)
            except Exception as e:
                return {"status": 0, "data": {"text": f'{self.unique_id} 发送私信 data 编码出错：{e}', "response_data": [data, typedef]}, "login_expired": 0, "chat_id": chat_id}
        else:
            data = message_data

        try:
            response: "Response" = self.do_req(method="POST", url=self.join_url_params("https://im-api.tiktok.com/v1/message/send", params), headers=headers, data=data)
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 发送私信请求出错：{e}", "response_data": ""}, "login_expired": 0, "chat_id": chat_id}
        return self.parse_chat_result(response_content=response.content, message_text=message_text, chat_id=chat_id)