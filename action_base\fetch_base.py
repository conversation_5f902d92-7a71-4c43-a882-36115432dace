import json
import re
import time
from typing import Union, Dict, Any

import requests
from requests.exceptions import HTTP<PERSON>rror, RequestException
from urllib3.exceptions import InsecureRequestWarning

from action_base.tkBase import TkBase
from utils.data_filters import (
    filter_user_profile,
    filter_video_list, filter_user_list, filter_comment_list, filter_single_video_data, remove_none_values
)
from utils.errors import NetworkError, ServiceError
from utils.tk_algorithm import get_XBogus, get_signature, tk_urlencode_2

requests.packages.urllib3.disable_warnings(InsecureRequestWarning)


class UserInfoProtocolError(ServiceError):
    pass


class VideoProtocolError(ServiceError):
    pass


class FetchBase(TkBase):
    def __init__(self, config=None, cookies_dict=None, proxies=None, **kwargs):
        super().__init__(
            config=config,
            cookies_dict=cookies_dict,
            proxies=proxies,
            **kwargs
        )

    def _get_user_info_from_webpage(self, username: str) -> Dict[str, Any]:
        url = f"https://www.tiktok.com/@{username}"
        headers = {
            'Accept': '*/*', 'Host': 'www.tiktok.com', 'Connection': 'keep-alive',
            "User-Agent": self.config["USER_AGENT"],
        }
        try:
            response = self.do_request(method="GET", url=url, headers=headers, verify=False, timeout=self.max_req_timeout)
            response.raise_for_status()

            pattern = r'"webapp\.user-detail":({.*?}),"webapp'
            match = re.search(pattern, response.text)

            if match:
                user_data = json.loads(match.group(1))
                if 'userInfo' in user_data:
                    now_time = int(time.time())
                    original_user_info = user_data.get('userInfo', {})
                    filtered_user_info = filter_user_profile(original_user_info, source="web_page")

                    raw_user = original_user_info.get("user", {})
                    if raw_user:
                        if "region" in raw_user:
                            filtered_user_info["user"]["region"] = raw_user["region"]
                        if "language" in raw_user:
                            filtered_user_info["user"]["language"] = raw_user["language"]

                    return {"status": 1, "data": {"userInfo": filtered_user_info, "statusCode": 0, "collectTime": now_time}}
            return {"status": 0, "error": "未找到用户"}

        except requests.RequestException as e:
            raise UserInfoProtocolError(f"获取用户网页信息时发生网络错误: {e}") from e
        except Exception as e:
            raise UserInfoProtocolError(f"解析用户网页信息时发生异常: {e}") from e

    def _get_user_info(self, sec_uid: str = None, unique_id: str = None, prefer_api: bool = False):
        if sec_uid and unique_id:
            unique_id = None
        if unique_id and unique_id.isdigit():
            return {"status": 1, "data": {"userInfo": {}}, "not_exist": True}
        if unique_id and not prefer_api:
            try:
                webpage_result = self._get_user_info_from_webpage(unique_id)
                if webpage_result.get("status") == 1:
                    return webpage_result
            except UserInfoProtocolError:
                pass  # 网页获取失败，继续尝试API

        try:
            url = "https://www.tiktok.com/api/user/detail/"
            now_time = int(time.time())
            params = {
                "WebIdLastTime": now_time, "aid": "1988", "app_language": "en", "app_name": "tiktok_web",
                "browser_language": "zh-SG", "browser_name": "Mozilla", "browser_online": "true",
                "browser_platform": "Win32", "browser_version": self.BROWSER_VERSION, "channel": "tiktok_web",
                "cookie_enabled": "true", "device_id": self.device_id or self.config["DEFAULT_DEVICE_ID"],
                "device_platform": "web_pc",
                "focus_state": "true", "from_page": "user", "history_len": "4", "is_fullscreen": "false",
                "is_page_visible": "true", "language": "en", "os": "windows", "priority_region": "CN", "referer": "",
                "region": "JP", "screen_height": "864", "screen_width": "1536",
                "uniqueId": unique_id or "", "secUid": sec_uid or "",
                "webcast_language": "en", "msToken": self.config["default_ms_token"],
                "coverFormat": "2",
                "needPinnedItemIds": "true", "post_item_list_request_type": "0", "count": "2", "cursor": "0"
            }
            params["X-Bogus"] = get_XBogus(params=params, ua=self.ua, now_time=now_time,
                                           canvas_num=self.canvas_num or self.config["canvas_num"])
            params["_signature"] = get_signature(X_Bogus=params["X-Bogus"], ua=self.ua)

            request_url = self.join_url_params(url, params)
            response = self.do_request(
                method="GET",
                url=request_url,
                headers={"user-agent": self.ua},
                cookies={"tt-target-idc": "useast8"},
                timeout=self.max_req_timeout,
                verify=False
            )

            if response.status_code == 400:
                result = response.json()
                if result.get("statusCode") in [10221, 10202]:
                    return {"status": 1, "data": {"userInfo": result.get("userInfo", {"data_source": "api"}), "statusCode": result.get("statusCode")}, "not_exist": True}

            response.raise_for_status()
            result = response.json()

            if result.get("statusCode") in [0, 10222]:
                userInfo = result.get("userInfo", {})
                if not userInfo:
                    raise UserInfoProtocolError("获取用户信息成功，但内容为空")
                filtered_user_info = filter_user_profile(result, source="api")
                return {"status": 1, "data": {"userInfo": filtered_user_info}}
            elif result.get("statusCode") in [10221, 10202, 100002]:
                user_info = result.get("userInfo", {})
                filtered_user_info = filter_user_profile(user_info, source="api")
                return {"status": 1, "data": {"userInfo": filtered_user_info, "statusCode": result.get("statusCode")}, "not_exist": True}
            else:
                raise UserInfoProtocolError(f"获取用户信息返回错误: {result.get('statusCode')}")

        except requests.RequestException as e:
            raise UserInfoProtocolError(f"获取用户信息时发生网络错误: {e}") from e
        except Exception as e:
            raise UserInfoProtocolError(f"处理用户信息API响应时发生异常: {e}") from e

    def get_user_detail(self, sec_uid: str = None, unique_id: str = None):
        return self._get_user_info(sec_uid, unique_id)

    def get_user_videos(self, user_identifier: str, cursor: str = "0", count: int = 35, **kwargs):
        if not user_identifier:
            return {"status": 0, "data": {"text": "用户标识符不能为空"}}

        try:
            if len(user_identifier) > 40:
                sec_uid = user_identifier
            else:
                # 注意：此处调用的是高层封装，不是底层的 _get_user_info
                user_info_result = self.fetch_user_info(user_identifier=user_identifier)
                if not user_info_result.get("status"):
                    return {"status": 0, "data": {"text": user_info_result.get("data", {}).get("text", "获取用户信息失败")}}

                if user_info_result.get("not_exist"):
                    return {
                        "status": 1,
                        "not_exist": True,
                        "data": {
                            "itemList": [], "cursor": "-1", "hasMore": False,
                            "statusCode": user_info_result.get("data", {}).get("response_data", {}).get("statusCode")
                        }
                    }

                user_info_data = user_info_result.get("data", {}).get("response_data", {})
                sec_uid = user_info_data.get("user", {}).get(
                        "secUid") if "user" in user_info_data else user_info_data.get(
                        "secUid")

            if not sec_uid:
                return {"status": 0, "data": {"text": "无法获取用户secUid"}}

            url = "https://www.tiktok.com/api/post/item_list/"
            now_time = int(time.time())
            params = {
                "aid": "1988", "app_language": "en", "count": str(count), "cursor": str(cursor), "secUid": sec_uid,
                "WebIdLastTime": now_time, "app_name": "tiktok_web", "browser_language": "zh-SG",
                "browser_name": "Mozilla",
                "browser_online": "true", "browser_platform": "Win32", "browser_version": self.BROWSER_VERSION,
                "channel": "tiktok_web",
                "cookie_enabled": "true", "device_id": self.device_id or self.config["DEFAULT_DEVICE_ID"],
                "device_platform": "web_pc",
                "focus_state": "true",
                "from_page": "user", "history_len": "4", "is_fullscreen": "false", "is_page_visible": "true",
                "language": "en", "os": "windows", "priority_region": "CN", "referer": "", "region": "JP",
                "screen_height": "864", "screen_width": "1536", "tz_name": "Asia/Shanghai",
                "user_is_login": "true", "webcast_language": "en",
                "msToken": self.config["default_ms_token"]
            }
            params["X-Bogus"] = get_XBogus(params=params, ua=self.ua, now_time=now_time,
                                           canvas_num=self.canvas_num or self.config["canvas_num"])
            params["_signature"] = get_signature(X_Bogus=params["X-Bogus"], ua=self.ua)
            request_url = self.join_url_params(url, params)

            headers = {"user-agent": self.ua}
            cookies = {"tt-target-idc": "useast8"}

            response = self.do_request(
                method="GET",
                url=request_url,
                headers=headers,
                cookies=cookies,
                verify=False,
                timeout=self.max_req_timeout
            )
            response.raise_for_status()

            if not response.text or response.text.strip() == "":
                raise ServiceError("TikTok API返回空响应，可能被风控")

            result = response.json()

            if result.get("status_code") == 0:
                filtered_result = filter_video_list(result)
                return {"status": 1, "data": filtered_result}
            else:
                raise VideoProtocolError(f"获取视频列表返回错误: {result.get('status_msg')}")

        except HTTPError as e:
            raise ServiceError(f"HTTP错误: {e.response.status_code}", error_code=str(e.response.status_code)) from e
        except RequestException as e:
            raise NetworkError(f"网络请求失败: {e}") from e
        except ValueError as e:
            raise ServiceError(f"JSON解析失败: {e}") from e
        except ServiceError as e:
            raise VideoProtocolError(f"获取用户视频时发生错误(ID:{user_identifier}): {e}") from e

    def get_video_by_id(self, video_id: str, small_task_id: Union[str, int] = None):
        if not video_id:
            return {"status": 0, "error": "视频ID不能为空"}

        try:
            url = "https://www.tiktok.com/api/item/detail/"
            now_time = int(time.time())
            params = {
                "WebIdLastTime": str(now_time), "aid": "1988", "app_language": "en", "app_name": "tiktok_web",
                "browser_language": "en-US", "browser_name": "Mozilla", "browser_online": "true",
                "browser_platform": "Win32", "browser_version": self.BROWSER_VERSION, "channel": "tiktok_web",
                "cookie_enabled": "true", "device_id": self.config["DEFAULT_DEVICE_ID"],
                "device_platform": "web_pc",
                "focus_state": "true", "from_page": "user", "history_len": "4", "is_fullscreen": "false",
                "is_page_visible": "true", "language": "en", "os": "windows", "priority_region": "US",
                "referer": "", "region": "US", "root_referer": "https://www.tiktok.com/",
                "screen_height": "1080", "screen_width": "1920", "webcast_language": "en",
                "itemId": video_id
            }
            X_Bogus = get_XBogus(params=params, data="", ua=self.ua, now_time=now_time,
                                 canvas_num=self.config["canvas_num"],
                                 extra_num=0)
            _signature = get_signature(url=url, params=params, X_Bogus=X_Bogus, data="", ua=self.ua,
                                       now_time=now_time, canvas_num=self.config["canvas_num"]
                                       )
            params["X-Bogus"] = X_Bogus
            params["_signature"] = _signature
            request_url = f"{url}?{tk_urlencode_2(params)}"

            response = self.do_request(
                method="GET",
                url=request_url,
                headers={"user-agent": self.ua},
                verify=False,
                timeout=self.max_req_timeout
            )

            if response.status_code == 400:
                error_data = response.json()
                if error_data.get("statusCode") in [10204, 10201]:
                    return {"status": 1, "data": {}, "not_exist": True,
                            "statusCode": error_data.get("statusCode")}
                else:
                    raise VideoProtocolError(f"获取视频详情返回未处理的400错误: {error_data}")

            response.raise_for_status()
            result = response.json()

            if result.get("statusCode") == 0:
                filtered_data = filter_single_video_data(result)
                if small_task_id:
                    filtered_data["small_task_id"] = small_task_id
                return {"status": 1, "data": filtered_data}
            elif result.get("statusCode") in [10204, 10201]:
                return {"status": 1, "data": {}, "not_exist": True, "statusCode": result.get("statusCode")}
            else:
                raise VideoProtocolError(f"获取视频详情返回错误: {result.get('statusMsg', '未知状态')}")

        except requests.RequestException as e:
            raise VideoProtocolError(f"获取视频详情时发生网络错误(ID:{video_id}): {e}") from e
        except Exception as e:
            raise ServiceError(f"处理视频详情时发生未知错误: {e}") from e

    def fetch_user_info(self, user_identifier: str):
        """采集用户详情"""
        try:
            if len(user_identifier) > 40:
                result = self._get_user_info(sec_uid=user_identifier)
            else:
                result = self._get_user_info(unique_id=user_identifier)

            if result.get("status") != 1:
                return {"status": 0, "data": {"text": f"采集失败: {result.get('error', '未知错误')}"},
                        "login_expired": 0}

            user_info = result.get("data", {}).get("userInfo", {})
            if result.get("not_exist"):
                return {"status": 2, "data": {"text": "账号不存在", "response_data": user_info}, "not_exist": True,
                        "login_expired": 0}

            return {"status": 2, "data": {"text": "采集成功", "response_data": user_info}, "login_expired": 0}

        except ServiceError as e:
            return {"status": 3, "data": {"text": f"采集失败: {e}"}, "login_expired": 0}

    def fetch_video_info(self, video_id: str):
        """采集视频详情"""
        try:
            result = self.get_video_by_id(video_id=video_id)
            if result.get("status") == 1:
                if result.get("not_exist"):
                    return {"status": 2, "data": {"text": "视频不存在", "response_data": result.get("data", {})},
                            "not_exist": True, "login_expired": 0}

                return {"status": 2,
                        "data": {"text": f"视频 [{video_id}] 信息获取成功", "response_data": result.get("data", {})},
                        "login_expired": 0}
            else:
                return {"status": 3,
                        "data": {"text": f"视频 [{video_id}] 信息获取失败: {result.get('error', '未知错误')}",
                                 "response_data": {}},
                        "login_expired": 0}
        except Exception as e:
            return {"status": 3,
                    "data": {"text": f"采集视频 [{video_id}] 信息时发生代码层面的错误: {e}", "response_data": ""},
                    "login_expired": 0}

    def get_follow_list(self, sec_uid: str, cursor: int = 0, count: int = 30, **kwargs):
        """
        获取指定用户的关注列表。
        """
        if not sec_uid:
            return {"status": 0, "error": "用户ID不能为空"}

        try:
            url = "https://www.tiktok.com/api/user/list/"
            params = {"count": str(count), "maxCursor": "0", "minCursor": str(cursor), "secUid": sec_uid}
            request_url = self.join_url_params(url, params)

            response = self.do_request("GET", request_url, headers={"user-agent": self.ua}, timeout=self.max_req_timeout)
            response.raise_for_status()

            result = response.json()
            status_code = result.get("statusCode")

            if status_code == 0:
                filtered_result = filter_user_list(result)
                return {"status": 1, "data": filtered_result}

            elif status_code in [10222, 10223]:
                return {
                    "status": 1,
                    "not_exist": True, # 标记为特殊状态，上游会处理为"账号不存在或私密"
                    "data": {
                        "userList": [], "hasMore": False, "maxCursor": "0",
                        "minCursor": cursor, "statusCode": status_code,
                    }
                }
            else:
                raise ServiceError(f"获取关注列表返回未处理的状态码: {status_code}")
        except Exception as e:
            raise ServiceError(f"获取关注列表时发生错误: {e}") from e

    def get_follower_list(self, sec_uid: str, cursor: int = 0, count: int = 30, **kwargs):
        """
        获取指定用户的粉丝列表。
        """
        if not sec_uid:
            return {"status": 0, "error": "用户ID不能为空"}
        try:
            url = "https://www.tiktok.com/api/user/list/"
            params = {"count": str(count), "maxCursor": "0", "minCursor": str(cursor), "secUid": sec_uid, "scene": "67"}
            request_url = self.join_url_params(url, params)
            response = self.do_request("GET", request_url, headers={"user-agent": self.ua}, timeout=self.max_req_timeout)
            response.raise_for_status()
            result = response.json()
            if result.get("statusCode") == 0:
                filtered_result = filter_user_list(result)
                return {"status": 1, "data": filtered_result}
            elif result.get("statusCode") == 10222:
                return {
                    "status": 1,
                    "data": {
                        "userList": [], "hasMore": False, "maxCursor": "0",
                        "minCursor": cursor, "statusCode": result.get("statusCode"),
                    }
                }
            else:
                raise ServiceError(f"获取粉丝列表失败: {result.get('statusCode')}")
        except Exception as e:
            raise ServiceError(f"获取粉丝列表时发生错误: {e}") from e

    def get_video_comments(self, video_id: str, cursor: int = 0, count: int = 50, **kwargs):
        """
        获取指定视频的评论列表。
        """
        if not video_id:
            return {"status": 0, "error": "视频ID不能为空"}
        try:
            url = "https://www.tiktok.com/api/comment/list/"
            params = {
                "aweme_id": video_id, "count": str(count), "cursor": str(cursor), "aid": "1988",
                "device_id": self.device_id or self.config["DEFAULT_DEVICE_ID"]
            }
            request_url = self.join_url_params(url, params)
            response = self.do_request("GET", request_url, headers={"user-agent": self.ua}, timeout=self.max_req_timeout)
            response.raise_for_status()
            result = response.json()
            
            if result.get("status_code") == 0:
                comments = result.get("comments")
                if comments is None:
                    return {
                        "status": 1,
                        "not_exist": True,
                        "data": {
                            "commentList": [], "cursor": result.get("cursor", "0"), "hasMore": False,
                             "statusCode": 0
                        }
                    }
                filtered_result = filter_comment_list(result)
                return {"status": 1, "data": filtered_result}
            elif result.get("status_code") in [5, 10204]:
                 return {
                     "status": 1,
                     "not_exist": True,
                     "data": {
                        "commentList": [], "cursor": "0", "hasMore": False,
                        "statusCode": result.get("status_code")
                     }
                }
            else:
                raise ServiceError(f"获取评论列表失败, status_code: {result.get('status_code')}")
        except Exception as e:
            raise ServiceError(f"获取评论时发生错误: {e}") from e

    def get_comment_replies(self, comment_id: str, item_id: str, cursor: int = 0, count: int = 50, **kwargs):
        """
        获取指定评论的回复列表。
        """
        if not comment_id or not item_id:
            return {"status": 0, "error": "评论ID和视频ID不能为空"}
        try:
            url = "https://www.tiktok.com/api/comment/list/reply/"
            params = {
                "aid": "1988",
                "comment_id": comment_id,
                "count": str(count),
                "cursor": str(cursor),
                "item_id": item_id,
                "device_id": self.device_id or self.config["DEFAULT_DEVICE_ID"]
            }
            request_url = self.join_url_params(url, params)
            response = self.do_request("GET", request_url, headers={"user-agent": self.ua}, timeout=self.max_req_timeout)
            response.raise_for_status()
            result = response.json()

            if result.get("status_code") == 0:
                # 清理数据中的None值
                cleaned_comments = remove_none_values(result.get("comments", []))
                final_data = {
                    "commentList": cleaned_comments,
                    "cursor": result.get("cursor"),
                    "has_more": result.get("has_more"),
                    "total": result.get("total"),
                    "status_code": result.get("status_code")
                }
                return {"status": 1, "data": final_data}
            elif result.get("status_code") in [5, 10204]:
                return {
                    "status": 1,
                    "not_exist": True,
                    "data": {
                        "commentList": [], "cursor": "0", "hasMore": False,
                        "statusCode": result.get("status_code")
                    }
                }
            else:
                raise ServiceError(f"获取评论回复列表失败, status_code: {result.get('status_code')}")
        except Exception as e:
            raise ServiceError(f"获取评论回复时发生错误: {e}") from e
