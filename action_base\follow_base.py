import time
from utils import group_XBogus_XGnarly
from action_base.tkBase import TkBase

class FollowBase(TkBase):
    def comment_video(self, author_unique_id="", video_id="", comment_text="", text_extra=[], cid="", reply_to_reply_id="0"):
        now_time = int(time.time() * 1000)
        headers = {
            "accept": "*/*",
            "accept-language": "en-US,en;q=0.9",
            "cache-control": "no-cache",
            "content-length": "0",
            "content-type": "application/x-www-form-urlencoded",
            "origin": "https://www.tiktok.com",
            "pragma": "no-cache",
            "priority": "u=1, i",
            "sec-ch-ua-mobile": "?0",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "tt-csrf-token": self.session.cookies.get_dict().get("tt_csrf_token"),
            "user-agent": self.ua
        }
        if author_unique_id:
            headers["referer"] = f"https://www.tiktok.com/@{author_unique_id}/video/{video_id}"
        params = {
            "WebIdLastTime": self.WebIdLastTime,
            "aid": "1988",
            "app_language": "en",
            "app_name": "tiktok_web",
            "aweme_id": video_id, # 视频 id
            "browser_language": "zh-CN",
            "browser_name": self.ua.split("/")[0], # Mozilla,
            "browser_online": "true",
            "browser_platform": "Win32",
            "browser_version": self.BROWSER_VERSION, # '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
            "channel": "tiktok_web",
            "cookie_enabled": "true",
            "data_collection_enabled": "true",
            "device_id": self.device_id,
            "device_platform": "web_pc",
            "focus_state": "true",
            "from_page": "video",
            # "history_len": "12",
            "is_fullscreen": "false",
            "is_page_visible": "true",
            "odinId": self.user_id,
            "os": "windows",
            "priority_region": "SG",
            "referer": "",
            "region": "SG",
            "screen_height": "1080",
            "screen_width": "1920",
            "text": comment_text,
            "text_extra": str(text_extra),
            "tz_name": self.tz_name,
            "user_is_login": "true",
            "webcast_language": "en",
            "msToken": self.session.cookies.get_dict().get("msToken")
        }
        if cid:
            params["reply_id"] = cid # 回复的一级评论的 id
            params["reply_to_reply_id"] = reply_to_reply_id if reply_to_reply_id else "0" # 回复某一条二级评论的 id
        params, data = group_XBogus_XGnarly(
            params=params,
            data="",
            no_json=True, 
            ua=self.ua,
            now_time=now_time,
            canvas_num=self.canvas_num, 
            extra_num=0,
            page_sign=self.page_sign
        )
        try:
            response = self.do_req(method="POST", url=self.join_url_params("https://www.tiktok.com/api/comment/publish/", params), headers=headers)
        except Exception as e:
            return {"status": 0, "data": {"text": f'{self.unique_id} 评论视频 {video_id} 请求出错：{e}', "response_data": ""}, "login_expired": 0}
        
        try:
            response_json: dict = response.json()
            comment = response_json.get("comment", {})
            status_msg = response_json.get("status_msg", "")
            if comment and status_msg.lower() == "comment sent successfully":
                return {"status": 1, "data": {"text": f"{self.unique_id} 评论视频 {video_id} 成功", "response_data": comment}, "login_expired": 0}
            return {"status": 0, "data": {"text": f"{self.unique_id} 评论视频 {video_id} 失败", "response_data": response_json}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f'{self.unique_id} 评论视频 {video_id} 响应内容解析出错：{e}', "response_data": ""}, "login_expired": 0}