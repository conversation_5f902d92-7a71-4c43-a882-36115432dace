import json
from functools import lru_cache
from pathlib import Path
from typing import Optional, List

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    # Core settings
    cookies: str = ""
    proxy_url: Optional[str] = None
    default_ms_token: Optional[str] = None
    # 鉴权密钥 (可以是一个或多个)
    api_secret_key: Optional[List[str]] = None
    # 使用别名来匹配 config.json 中的大写键
    user_agent: str = Field(
        alias="USER_AGENT"
    )

    # Request settings
    max_req_timeout: int = 30
    # 使用别名来匹配 config.json 中的大写键
    default_device_id: str = Field(default="7318518423832348166", alias="DEFAULT_DEVICE_ID")

    # Algorithm settings
    canvas_num: int = 1

    # For lifespan management
    token_update_interval_hours: int = 24

    model_config = SettingsConfigDict(
        # 允许从 .env 文件读取环境变量
        env_file=".env",
        env_file_encoding='utf-8',
        # 我们将动态提供 json_file 的绝对路径
        json_file_encoding='utf-8',
        # 忽略未在模型中定义的额外字段
        extra='ignore'
    )


@lru_cache()
def get_settings() -> Settings:
    """
    返回一个缓存的 Settings 实例，并确保从正确的 config.json 加载。
    """
    # 从当前文件位置推断项目根目录并构造 config.json 的绝对路径
    # __file__ -> config.py -> .parent -> project_root
    project_root = Path(__file__).resolve().parent.parent
    config_path = project_root / "config.json"

    if config_path.exists():
        # 如果文件存在，手动加载它并作为关键字参数传递
        with config_path.open(encoding='utf-8') as f:
            config_data = json.load(f)
        return Settings(**config_data)
    else:
        # 如果文件不存在，则使用默认值并打印警告
        print(f"警告: 配置文件未在 '{config_path}' 找到。将使用默认设置。")
        return Settings()
