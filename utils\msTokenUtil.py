import logging
from json import dumps
from time import time
from typing import Optional, Dict

import httpx
import requests

from utils import tk_algorithm

MS_API_URL = "https://mssdk-ttp2.tiktokw.us/web/report"
REQUEST_TIMEOUT = 30.0


def _prepare_request_data(config, token):
    """准备TikTok msToken请求的公共数据"""
    user_agent = config["user_agent"]

    headers = {
        "Accept": "*/*",
        "Accept-Encoding": "*/*",
        "Content-Type": "text/plain;charset=UTF-8",
        "Referer": "https://www.tiktok.com/explore",
        "User-Agent": user_agent,
    }

    params = {"msToken": token}

    if token:
        headers["Cookie"] = f"msToken={token}"

    # 使用项目统一的X-Bogus函数
    bogus_params, _ = tk_algorithm.group_XBogus_XGnarly(
        params=params,
        data="",
        no_json=True,
        ua=user_agent,
        now_time=int(time() * 1000),
        canvas_num=config["canvas_num"],
    )
    params = bogus_params

    data = {
        "magic": 538969122,
        "version": 1,
        "dataType": 8,
        "strData": "3DWMSoJNifh/BoM1CDv7lbH3G7vd6C7zPt0YWMVrYRi369yWaBxCOhq+WMznjr1QWKkr/uLgcnRh+LQDtMl/JDLHSPlEqNPz/iuxeOktia3YM/pJtUX4EQYqBMW8uAx4qFcN8M5H5XhB1FEkk76W09Xq5DwtcjoO4dpH18G3UcI1hasCXVW8B+igwPIeEuOIayxuf3OZlTmZbNI1guSUBbccxoph0SEb1TVc4/DeQjQvXkXZOmuN144LcENdtflWmcQPqcwnfD2bWGuR4+LUgRke1GcyVYa440PH/VOm+DYNcbKeBG87gqTHg+Y724ph1RQKlKX4nsi7Wa+V08ESimNbT8DMsbA",
        "tspFromClient": int(time() * 1000),
        "ulr": 0,
    }

    return headers, params, data


def _parse_response(response, logger):
    """解析TikTok msToken响应的公共逻辑"""
    status_code = response.status_code
    if status_code != 200:
        logger.error(f"[msToken] 请求失败 | 状态码: {status_code}")
        return None

    response.raise_for_status()

    if c := response.headers.get("Set-Cookie"):
        # 简单解析msToken，不依赖http.cookies
        for part in c.split(';'):
            if 'msToken=' in part:
                new_token = part.strip().replace('msToken=', '')
                if new_token and new_token != 'deleted':
                    logger.debug(f"[msToken] 更新成功 | 新token: {new_token[:15]}...")
                    return {"msToken": new_token}

    logger.error("[msToken] 更新失败 | 响应中无有效token")
    return None


def _log_proxy_usage(logger, proxy):
    """记录代理使用情况"""
    if proxy:
        logger.debug(f"[msToken] 使用代理: {proxy}")


def _handle_exception(logger, e):
    """处理异常的公共逻辑"""
    error_type = type(e).__name__
    logger.error(f"[msToken] 更新异常 | 类型: {error_type} | 详情: {str(e)}")
    raise


async def get_tiktok_token_async(config=None, token="", proxy=None) -> Optional[Dict]:
    """通过调用TikTok的Web Report API来获取或刷新msToken(异步版本)"""
    logger = logging.getLogger(__name__)
    headers, params, data = _prepare_request_data(config, token)

    try:
        proxies = proxy if proxy else None
        _log_proxy_usage(logger, proxy)
        logger.debug("[msToken] 开始请求更新")

        async with httpx.AsyncClient(
                proxy=proxies,
                timeout=REQUEST_TIMEOUT,
                verify=False,
                follow_redirects=True
        ) as client:
            response = await client.post(
                MS_API_URL,
                headers=headers,
                params=params,
                data=dumps(data),
            )

            # 使用公共函数解析响应
            return _parse_response(response, logger)

    except Exception as e:
        _handle_exception(logger, e)


def get_tiktok_token(config=None, token="", proxy=None) -> Optional[Dict]:
    """通过调用TikTok的Web Report API来获取或刷新msToken"""
    logger = logging.getLogger(__name__)
    headers, params, data = _prepare_request_data(config, token)

    try:
        proxies = {"http": proxy, "https": proxy} if proxy else None
        _log_proxy_usage(logger, proxy)
        logger.debug("[msToken] 开始请求更新")

        response = requests.post(
            MS_API_URL,
            headers=headers,
            params=params,
            data=dumps(data),
            proxies=proxies,
            timeout=REQUEST_TIMEOUT,
            verify=False,
            allow_redirects=True,
        )

        # 使用公共函数解析响应
        return _parse_response(response, logger)

    except Exception as e:
        _handle_exception(logger, e)
