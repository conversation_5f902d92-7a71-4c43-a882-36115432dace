from action_flow import *

# 发视频
def do_publish(init_data={}, 
    video_info={},
    tcm_params={"commerce_toggle_info": {}}, # 默认或 有了 brand_organic_type、branded_content_type
    content_check_id=True, # 不知道什么东西，默认开启（其实好像有没有这个参数都无所谓）
    video_size=0, # 发布的每个视频的字节大小
):
    publishFlow = PublishFlow(**init_data)
    result = publishFlow.publish_video_flow(
        video_info=video_info,
        tcm_params=tcm_params,
        content_check_id=content_check_id,
        video_size=video_size
    )
    result["cookie_dict"] = publishFlow.session.cookies.get_dict()
    return result

# 用户信息修改
def do_modify(init_data={}, task_type="", 
    new_unique_id="",
    profile_img_url="",
    new_nickname="",
    signature_text="",
    setting_type=0,
):
    modifyFlow = ModifyFlow(**init_data)
    async_init_result = modifyFlow.sync_init()
    if (async_init_result is not None) and (not async_init_result.get("status")):
        result = {"status": 0, "data": {"text": f'{modifyFlow.unique_id} 个人操作/信息修改初始化失败', "response_data": async_init_result}, "login_expired": 0}
    else:
        get_settings_result = modifyFlow.get_settings()
        if not get_settings_result.get("status"):
            result = get_settings_result
        else:
            if task_type == "unique_id": # 修改唯一 id
                result = modifyFlow.change_unique_id_flow(new_unique_id=new_unique_id)
            elif task_type == "avatar": # 修改头像
                result = modifyFlow.change_profile_pic(profile_img_url=profile_img_url)
            elif task_type == "nicknames_signatures": # 修改昵称与签名
                result = modifyFlow.change_nicknames_signatures(new_nickname=new_nickname, signature_text=signature_text)
            elif task_type == "private_account": # "私密账号公开账号的设置duet、Stitch、download"
                result = modifyFlow.set_private_account_flow(setting_type=setting_type)
            else:
                result = {"status": 0, "data": {"text": f'{modifyFlow.unique_id} 修改/操作无 {task_type} 类型', "response_data": task_type}, "login_expired": 0}
    result["cookie_dict"] = modifyFlow.session.cookies.get_dict()
    return result

# 私信
def do_chat(init_data={}, receiver_username="", receiver_userid="", 
    message_text="", 
    recommend_emoji=False, emoji_keyword="", emoji_info={}, 
    link_info={}, 
    share_video_info={}, 
    share_place_info={}, 
    message_data=b'', 
    chat_info="", chat_id=0, 
    use_app=False,
):
    chatFlow = ChatFlow(**init_data)
    async_init_result = chatFlow.sync_init() if chatFlow.unique_id else None
    if (async_init_result is not None) and (not async_init_result.get("status")):
        result = {"status": 0, "data": {"text": f'{chatFlow.unique_id} 发送私信初始化失败', "response_data": async_init_result}, "login_expired": 0}
    else:
        result = chatFlow.tk_message_send_flow(
            receiver_username=receiver_username, receiver_userid=receiver_userid,
            message_text=message_text, 
            recommend_emoji=recommend_emoji, emoji_keyword=emoji_keyword, emoji_info=emoji_info, 
            link_info=link_info, 
            share_video_info=share_video_info, 
            share_place_info=share_place_info, 
            message_data=message_data, 
            chat_info=chat_info, chat_id=chat_id, 
            use_app=use_app
        )
    result["cookie_dict"] = chatFlow.session.cookies.get_dict()
    return result

def do_follow(init_data, 
    author_unique_id="", video_id="", comment_text="", user_id_map={},
    cid="", reply_to_reply_id=""
):
    followFlow = FollowFlow(**init_data)
    async_init_result = followFlow.sync_init() if followFlow.unique_id else None
    if (async_init_result is not None) and (not async_init_result.get("status")):
        result = {"status": 0, "data": {"text": f'{followFlow.unique_id} 互动初始化失败', "response_data": async_init_result}, "login_expired": 0}
    else:
        result = followFlow.comment_video_flow(
            author_unique_id=author_unique_id, 
            video_id=video_id,
            comment_text=comment_text,
            user_id_map=user_id_map,
            cid=cid,
            reply_to_reply_id=reply_to_reply_id
        )
    result["cookie_dict"] = followFlow.session.cookies.get_dict()
    return result

def do_fetch(init_data_dict, task_type, object_id, callback=None, task_id=None, small_task_id=None, **kwargs):
    """
    统一的采集任务入口。
    """
    fetch_init_params = {
        "config": init_data_dict.get("config", {}),
        "cookies_dict": init_data_dict.get("cookies_dict", {}),
        "proxies": init_data_dict.get("proxies")
    }
    fetch = FetchFlow(**fetch_init_params)

    # 将所有需要向下游传递的参数打包
    flow_kwargs = {
        "callback": callback,
        "task_type": task_type,
        "task_id": task_id,
        "small_task_id": small_task_id,
        **kwargs
    }

    if task_type == "video_id_collect":
        if not callback:
            raise ValueError("video_id_collect 任务需要一个回调函数")
        result = fetch.get_user_videos_flow(user_identifier=object_id, **flow_kwargs)
    elif task_type == "blogger_fans_collect":
        if not callback:
            raise ValueError("blogger_fans_collect 任务需要一个回调函数")
        result = fetch.get_follower_list_flow(user_identifier=object_id, **flow_kwargs)
    elif task_type == "blogger_follow_collect":
        if not callback:
            raise ValueError("blogger_follow_collect 任务需要一个回调函数")
        result = fetch.get_follow_list_flow(user_identifier=object_id, **flow_kwargs)
    elif task_type == "comment_collect":
        if not callback:
            raise ValueError("comment_collect 任务需要一个回调函数")
        result = fetch.get_video_comments_flow(video_id=object_id, **flow_kwargs)
    elif task_type == "comment_reply_collect":
        if not callback:
            raise ValueError("comment_reply_collect 任务需要一个回调函数")
        item_id = kwargs.get("item_id")
        if not item_id:
            raise ValueError("comment_reply_collect 任务需要在kwargs中提供 item_id (视频ID)")
        flow_kwargs.pop("item_id", None)
        result = fetch.get_comment_replies_flow(comment_id=object_id, item_id=item_id, **flow_kwargs)
    elif task_type == "video_collect":
        result = fetch.get_video_info_flow(video_id=object_id, **flow_kwargs)
    elif task_type == "blogger_collect":
        # blogger_collect 是单次任务，理论上不需要回调，但为了统一，也传入
        result = fetch.get_user_info_flow(user_identifier=object_id, **flow_kwargs)
    else:
        result = {"status": 0, "data": {"text": f"未知的采集任务类型: {task_type}"}}
    return result


if __name__ == "__main__":
    import time
    import json
    import os

    # 从 config.json 加载配置
    try:
        with open(r'D:\_mywork\tk\tk_project\config.json', 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        proxy_url = config_data.get("proxy_url")
    except (FileNotFoundError, json.JSONDecodeError):
        config_data = {}
        proxy_url = None

    proxies = {"http": proxy_url, "https": proxy_url} if proxy_url else None


    init_data_dict = {
        "config": config_data,
        "unique_id": "oxmoxmoxmoxm", # 修改唯一 id 后，后续需要重新相关实例的接口都需要注意变更，虽然不变更也能获取csrfToken
        "cookies_dict": {
            "tt-target-idc-sign": "ppLJtdRUdI8TzHWl0bVcOws5QsGMi56uC-ewLQ0tW9fti7pvNdBPc0gNbD151C0ZbgLGKd3mOZ0S_Y_QQwR247aEU7J5Xi-CbkIWPIVsjHXktHrJitLArZh0hpqSysbx4aFMWaIWmrk8QoHBFlyzRpDF7L8jeKyB9eKDv79XqDUNNtLLDypejUiFTmHNwHNCboEn2-yHQciVqm0SYbDeO80JuOTAsH4ieYuEGZpzn-LorCW1soRlEnUkFkqi1zCIajMZL3aHNzHgUvdsqKCLLjIBHgisyx_8h4_0i17F9p-MC21iBiezl34tMW7BSQq1elCrVi6TQC3t2oJc5oqVuRV0-HrH0Xy2cDWD9gtv9TBvqI0tTWZLXmn6GI1qmuW_HTo89kWvpFCNAWOaXUyaTtNsl5qhVIK2pKvXWtgvHf1_TlAf8KxC-1AYj5trglwKxLPHGlFX4JilUdJGCy0QMDki0M6_1gi9gEoiif_n_QT9HWcwxKG5mN4ckGlMKPpf",
            "msToken": "wL7P_4HoNKY7y7ASIL1rXxZUprwopEq-lCMg_Q_2h1WRl6amdV-0h0kkKXPLwPyNcjpUhU3RAH1IdR2Rg24nV0nlotAZmzUn_ZtxPK3Kak1dV2hnAZonk00FSgQWY6dY1suHHQVTRRQGoQltq9nr8-x-",
            "sid_guard": "23fa4e390184e9c956d3aba444116e9f%7C1751508698%7C15551996%7CTue%2C+30-Dec-2025+02%3A11%3A34+GMT",
            "ttwid": "1%7CgSu5bFeW1wYzN91HlTRp9Omr2FZQN3lhOjZpYMwJd7M%7C1751850832%7C4155c98219b5a081005bbc37b725f264b79f7214a1d045cda3161f535681a4d7",
            "tt-target-idc": "alisg",
        },
        "canvas_num": 2010578131,
        # "publish_baseurl": "http://127.0.0.1:8002/static/video", # "http://*************:52008/api/v1/files/download"
        "proxies": None
    }
    # result = do_publish(
    #     init_data=init_data_dict,
    #     video_info={
    #         "ai_generated_content": False,
    #         "allow_comment": 0,
    #         "allow_duet": 0,
    #         "allow_stitch": 0,
    #         "brand_organic_type": 0,
    #         "branded_content_type": 0,
    #         "copyright_music_check": False,
    #         "description": "修狗",
    #         "platform_id": 0,
    #         "schedule_time": None,
    #         "url": "69eda81942ec41b7945d40474a3acd0f.mp4",
    #         "visibility_type": 0
    #     },
    #     video_size=310626
    # )
    result = do_follow(
        init_data=init_data_dict,
        author_unique_id="yaleybjy1145141919810",
        video_id="7478239514595527967",
        comment_text="不对呢@funnystrange9 😂",
        user_id_map={"funnystrange9": "7468576998027723807"},
        # cid="7524242088037270280",
        # reply_to_reply_id="0"
    )
    print(result)

    # ============================== 采集测试 ==============================
    init_data_dict = {
        "config": config_data,
        "cookies_dict": config_data.get("cookies_dict", {}),
        "proxies": proxies,
        "ua": config_data.get("USER_AGENT"),
        "ms_token": config_data.get("cookies_dict", {}).get("msToken") or config_data.get("default_ms_token"),
        "device_id": config_data.get("DEFAULT_DEVICE_ID"),
        "canvas_num": config_data.get("canvas_num")
    }


    # print("\n--- 1. 测试采集用户信息 ---")
    # start_time = time.time()
    # user_fetch_result = do_fetch(
    #     init_data_dict=init_data_dict,
    #     task_type="blogger_collect",
    #     object_id="gbatigazzi2"
    # )
    # print(user_fetch_result)
    # print(f"耗时: {time.time() - start_time:.2f}秒")
    #
    # print("\n--- 2. 测试采集视频信息 ---")
    # start_time = time.time()
    # video_fetch_result = do_fetch(
    #     init_data_dict=init_data_dict,
    #     task_type="video_collect",
    #     object_id="7502551047378832671"
    # )
    # print(video_fetch_result)
    # print(f"耗时: {time.time() - start_time:.2f}秒")

    def my_video_callback(videos):
        print(f"\n--- 收到一批 {len(videos)} 个视频 ---")
        for video in videos:
            print(f"  - 视频ID: {video.get('id')}")


    all_fetched_users = []


    def my_user_callback(users):
        print(f"\n--- 收到一批 {len(users)} 个用户 ---")
        all_fetched_users.extend(users)
        for user in users:
            print(f"  - 用户: {user.get('uniqueId')} ({user.get('nickname')})")


    all_fetched_comments = []


    def my_comment_callback(comments):
        print(f"\n--- 收到一批 {len(comments)} 条评论 ---")
        all_fetched_comments.extend(comments)
        for comment in comments:
            print(f"  - 评论: {comment.get('text', '')[:30]}...")


    # print("\n--- 3. 测试采集用户视频ID列表 ---")
    # start_time = time.time()
    # UNIQUE_ID = "_376964"
    # # UNIQUE_ID = "adele"
    # # UNIQUE_ID = "reinaharo"
    # task_info = {"task_type": "video_id_collect", "object_id": UNIQUE_ID}
    #
    # videos_res = do_fetch(
    #     init_data_dict=init_data_dict,
    #     task_type=task_info["task_type"],
    #     object_id=task_info["object_id"],
    #     count=30,
    #     batch_size=1,
    #     callback=my_video_callback
    # )
    # print(f"\n视频ID列表采集结果: {videos_res}")
    # print(f"总耗时: {time.time() - start_time:.2f}秒")

    # print("\n--- 4. 测试采集用户关注列表 ---")
    # start_time = time.time()
    # UNIQUE_ID = "520.1314.2018.5.2.idle"
    # task_info = {"task_type": "blogger_follow_collect", "object_id": UNIQUE_ID}
    # all_fetched_users.clear()
    # follow_res = do_fetch(
    #     init_data_dict=init_data_dict,
    #     task_type=task_info["task_type"],
    #     object_id=task_info["object_id"],
    #     count=20,
    #     batch_size=10,
    #     callback=my_user_callback
    # )
    # print(f"\n关注列表采集结果: {follow_res}")
    # if all_fetched_users:
    #     timestamp = time.strftime("%Y%m%d-%H%M%S")
    #     filename = f"{task_info['task_type']}_{task_info['object_id']}_{timestamp}.json"
    #     with open(filename, 'w', encoding='utf-8') as f:
    #         json.dump(all_fetched_users, f, ensure_ascii=False, indent=2)
    #     print(f"已将 {len(all_fetched_users)} 个用户保存到 {filename}")
    # print(f"耗时: {time.time() - start_time:.2f}秒")

    # print("\n--- 5. 测试采集用户粉丝列表 ---")
    # start_time = time.time()
    # UNIQUE_ID = "520.1314.2018.5.2.idle"
    # task_info = {"task_type": "blogger_fans_collect", "object_id": UNIQUE_ID}
    # all_fetched_users.clear()
    # fans_res = do_fetch(
    #     init_data_dict=init_data_dict,
    #     task_type=task_info["task_type"],
    #     object_id=task_info["object_id"],
    #     count=20,
    #     batch_size=10,
    #     callback=my_user_callback
    # )
    # print(f"\n粉丝列表采集结果: {fans_res}")
    # if all_fetched_users:
    #     timestamp = time.strftime("%Y%m%d-%H%M%S")
    #     filename = f"{task_info['task_type']}_{task_info['object_id']}_{timestamp}.json"
    #     with open(filename, 'w', encoding='utf-8') as f:
    #         json.dump(all_fetched_users, f, ensure_ascii=False, indent=2)
    #     print(f"已将 {len(all_fetched_users)} 个用户保存到 {filename}")
    # print(f"耗时: {time.time() - start_time:.2f}秒")
    #
    # print("\n--- 6. 测试采集视频评论 ---")
    # start_time = time.time()
    # VIDEO_ID = "7502551047378832671"
    # task_info = {"task_type": "comment_collect", "object_id": VIDEO_ID}
    # all_fetched_comments.clear()
    # comments_res = do_fetch(
    #     init_data_dict=init_data_dict,
    #     task_type=task_info["task_type"],
    #     object_id=task_info["object_id"],
    #     count=30,
    #     batch_size=5,
    #     callback=my_comment_callback
    # )
    # print(f"\n评论采集结果: {comments_res}")
    # if all_fetched_comments:
    #     timestamp = time.strftime("%Y%m%d-%H%M%S")
    #     filename = f"{task_info['task_type']}_{task_info['object_id']}_{timestamp}.json"
    #     with open(filename, 'w', encoding='utf-8') as f:
    #         json.dump(all_fetched_comments, f, ensure_ascii=False, indent=2)
    #     print(f"已将 {len(all_fetched_comments)} 条评论保存到 {filename}")
    # print(f"耗时: {time.time() - start_time:.2f}秒")

    print("\n--- 7. 测试采集评论回复 ---")
    start_time = time.time()
    VIDEO_ID_FOR_REPLY = "7504285861761092871"
    COMMENT_ID_FOR_REPLY = "7504845829683135238"
    task_info = {"task_type": "comment_reply_collect", "object_id": COMMENT_ID_FOR_REPLY}
    all_fetched_comments.clear()
    reply_res = do_fetch(
        init_data_dict=init_data_dict,
        task_type=task_info["task_type"],
        object_id=task_info["object_id"],
        item_id=VIDEO_ID_FOR_REPLY,
        count=20,
        batch_size=10,
        callback=my_comment_callback
    )
    print(f"\n评论回复采集结果: {reply_res}")
    # if all_fetched_comments:
    #     timestamp = time.strftime("%Y%m%d-%H%M%S")
    #     filename = f"comment_reply_{task_info['object_id']}_{timestamp}.json"
    #     with open(filename, 'w', encoding='utf-8') as f:
    #         json.dump(all_fetched_comments, f, ensure_ascii=False, indent=2)
    #     print(f"已将 {len(all_fetched_comments)} 条评论回复保存到 {filename}")
    # print(f"耗时: {time.time() - start_time:.2f}秒")
