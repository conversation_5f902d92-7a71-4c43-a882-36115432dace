import time, uuid, blackboxprotobuf, json
from action_base import ChatBase
from utils import *

class ChatFlow(ChatBase):
    # 创建短链
    def get_shorten_url_flow(self, origin_url=""):
        try:
            if not origin_url:
                return {"status": 0, "data": {"text": f"{self.unique_id} 创建 {origin_url} 的短链未提供 origin_url", "response_data": origin_url}, "login_expired": 0}
            
            share_url = self.create_share_url(origin_url=origin_url)
            for i in range(2):
                get_shorten_url_result = self.get_shorten_url(share_url=share_url)
                if not get_shorten_url_result.get("status"):
                    return get_shorten_url_result
                share_url = get_shorten_url_result.get("data", {}).get("response_data", "")
            return get_shorten_url_result
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 创建 {origin_url} 的短链主流程出错：{e}", "response_data": ""}, "login_expired": 0}

    # 构造私信短链 data
    def create_link_info_data(self, receiver_username="", link_info={}, chat_info="", chat_id=0, use_app=False):
        if not link_info:
            return {"status": 0, "data": {"text": f'{self.unique_id} 发送短链卡片没有 link_info', "response_data": ""}, "login_expired": 0}
        
        try:
            deprecated_uuid = str(uuid.uuid4())
            message_dict = {
                "1": chat_info,
                "2": 1,
                "3": int(chat_id),
                "4": json.dumps({
                    "desc": link_info.get("desc", ""), # 描述
                    "cover_url": link_info.get("cover_url", ""), # 封面 url
                    "push_detail": "",
                    "title": link_info.get("title", ""), # 标题
                    "link_url": link_info.get("link_url", ""), # 链接地址
                    "is_card": False,
                    # "prev_conv_id": chat_info,
                    # "prev_id": str(chat_id),
                    "reference_scene": 0,
                    # "root_conv_id": chat_info,
                    # "root_id": str(chat_id),
                    # "root_relation_tag": "2",
                    "sendStartTime": round(time.time() * 1000),
                    "aweType": 0
                }, separators=(",", ":")),
                "5": [
                    {"1": "s:client_message_id", "2": str(uuid.uuid4())},
                    {"1": "im_callback_status_code", "2": "0"},
                    {"1": "a:disable_inner_push", "2": "1"},
                    {"1": "panel_source", "2": "share_panel"},
                    {"1": "a:disable_outer_push", "2": "1"},
                    {"1": "s:mode", "2": "0"},
                    {"1": "source_aid", "2": "1180"}
                ],
                "6": 26,
                "8": deprecated_uuid,
                "13": {},
                "14": {},
                "18": {},
                "19": {}
            }
            mid_typedef = {
                "field_order": ["1", "2", "3", "4", "5", "5", "5", "5", "5", "5", "5", "6", "8", "13", "14", "18", "19"],
                "message_typedef": {
                    "1": {"type": "string"},
                    "2": {"type": "int"},
                    "3": {"type": "int"},
                    "4": {"type": "string"},
                    "5": {
                        "seen_repeated": True,
                        "field_order": ["1", "2"],
                        "message_typedef": {
                            "1": {"type": "string"},
                            "2": {"type": "string"}
                        },
                        "type": "message"
                    },
                    "6": {"type": "int"},
                    "8": {"type": "string"},
                    "13": {"message_typedef": {}, "type": "message"},
                    "14": {"message_typedef": {}, "type": "message"},
                    "18": {"message_typedef": {}, "type": "message"},
                    "19": {"message_typedef": {}, "type": "message"}
                },
                "type": "message"
            }
            data = {
                "1": 100,
                "2": 283079,
                "3": "1.2.9",
                "4": {},
                "5": 3,
                "6": 0,
                "7": "",
                "8": {"100": message_dict},
                "9": self.device_id,
                "11": "android" if use_app else "web",
                # "15": [
                #     {"1": "aid", "2": "1988"},
                #     {"1": "app_name", "2": "tiktok_web"},
                #     {"1": "channel", "2": "web"},
                #     {"1": "device_platform", "2": "web_pc"},
                #     {"1": "device_id", "2": self.device_id},
                #     {"1": "region", "2": "US"},
                #     {"1": "priority_region", "2": "US"},
                #     {"1": "os", "2": "windows"},
                #     {"1": "referer", "2": f"https://www.tiktok.com/@{receiver_username}" if receiver_username else "https://www.tiktok.com/messages?lang=en"},
                #     {"1": "root_referer", "2": ""},
                #     {"1": "cookie_enabled", "2": "true"},
                #     {"1": "screen_width", "2": "1920"},
                #     {"1": "screen_height", "2": "1080"},
                #     {"1": "browser_language", "2": "en-US"},
                #     {"1": "browser_platform", "2": "Win32"},
                #     {"1": "browser_name", "2": self.ua.split("/")[0]}, # Mozilla
                #     {"1": "browser_version", "2": "/".join(self.ua.split("/")[1:])}, # '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
                #     {"1": "browser_online", "2": "true"},
                #     {"1": "verifyFp", "2": ""},
                #     {"1": "app_language", "2": "en"},
                #     {"1": "webcast_language", "2": "en"},
                #     {"1": "tz_name", "2": self.tz_name},
                #     {"1": "is_page_visible", "2": "true"},
                #     {"1": "focus_state", "2": "false"},
                #     {"1": "is_fullscreen", "2": "false"},
                #     {"1": "history_len", "2": "4"},
                #     {"1": "user_is_login", "2": "true"},
                #     {"1": "data_collection_enabled", "2": "true"},
                #     {"1": "from_appID", "2": "1988"},
                #     {"1": "locale", "2": "en"},
                #     {"1": "user_agent", "2": self.ua},
                #     # {"1": "Web-Sdk-Ms-Token", "2": ""} # 经测试不带也可以
                #     {"1": "Web-Sdk-Ms-Token", "2": self.session.cookies.get_dict().get("msToken")}
                # ],
                # "18": 1
            }
            typedef = {
                "1": {"type": "int"},
                "2": {"type": "int"},
                "3": {"type": "string"},
                "4": {"message_typedef": {}, "type": "message"},
                "5": {"type": "int"},
                "6": {"type": "int"},
                "7": {"type": "string"},
                "8": {
                    "field_order": ["100"],
                    "message_typedef": {
                        "100": mid_typedef
                    },
                    "type": "message"
                },
                "9": {"type": "string"},
                "11": {"type": "string"},
                # "15": {
                #     "seen_repeated": True,
                #     "field_order": ["1", "2"],
                #     "message_typedef": {"1": {"type": "string"}, "2": {"type": "string"}},
                #     "type": "message"
                # },
                # "18": {"type": "int"}
            }
            data = blackboxprotobuf.encode_message(data, typedef)
            return {"status": 1, "data": {"text": f'{self.unique_id} 发送私信短链卡片 data 编码成功', "response_data": data}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f'{self.unique_id} 发送私信短链卡片 data 编码出错：{e}', "response_data": ""}, "login_expired": 0}

    # 检查 video_info，自动填充
    def check_share_videoInfo(self, video_info={}, chat_id=0):
        try:
            if (not video_info.get("author")) or (not video_info.get("video")) or (not all(key in ["uniqueId", "nickname", "id", "secUid", "avatarThumb"] for key in video_info["author"])) or (not all(key in ["cover", "width", "height"] for key in video_info["video"])):
                if not video_info.get("id"):
                    return {"status": 0, "data": {"text": f"{self.unique_id} 分享视频未提供完整的 video_info", "response_data": ""}, "login_expired": 0, "chat_id": chat_id}
                get_video_info_result = self.get_video_info(video_id=video_info["id"])
                if not get_video_info_result.get("status"):
                    return get_video_info_result
                video_info = get_video_info_result.get("data", {}).get("response_data", {})
            return {"status": 1, "data": {"text": f"{self.unique_id} 分享视频 video_info 检查完毕", "response_data": video_info}, "login_expired": 0, "chat_id": chat_id}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 分享视频 video_info 检查出错：{e}", "response_data": ""}, "login_expired": 0, "chat_id": chat_id}

    # 创建公共的 video_data
    def get_public_share_video_data(self, seqId=10019, chat_info="", chat_id=0, video_info={}, use_app=False):
        try:
            itemId=video_info["id"]
            author=video_info["author"]
            video=video_info["video"]

            deprecated_uuid = str(uuid.uuid4())
            seqId = 10008 # seqId
            env_list = [
                {"1": "aid", "2": "1988"},
                {"1": "app_name", "2": "tiktok_web"},
                {"1": "channel", "2": "web"},
                {"1": "device_platform", "2": "web_pc"},
                {"1": "device_id", "2": self.device_id},
                {"1": "region", "2": "US"},
                {"1": "priority_region", "2": "US"},
                {"1": "os", "2": "windows"},
                {"1": "referer", "2": f'https://www.tiktok.com/@{author["uniqueId"]}/video/{itemId}'},
                {"1": "root_referer", "2": ""},
                {"1": "cookie_enabled", "2": "true"},
                {"1": "screen_width", "2": "1920"},
                {"1": "screen_height", "2": "1080"},
                {"1": "browser_language", "2": "en-US"},
                {"1": "browser_platform", "2": "Win32"},
                {"1": "browser_name", "2": self.ua.split("/")[0]}, # Mozilla
                {"1": "browser_version", "2": "/".join(self.ua.split("/")[1:])}, # '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
                {"1": "browser_online", "2": "true"},
                {"1": "verifyFp", "2": ""},
                {"1": "app_language", "2": "en"},
                {"1": "webcast_language", "2": "en"},
                {"1": "tz_name", "2": self.tz_name},
                {"1": "is_page_visible", "2": "true"},
                {"1": "focus_state", "2": "true"},
                {"1": "is_fullscreen", "2": "false"},
                # {"1": "history_len", "2": "4"},
                {"1": "user_is_login", "2": "true"},
                {"1": "data_collection_enabled", "2": "true"},
                {"1": "from_appID", "2": "1988"},
                {"1": "locale", "2": "en"},
                {"1": "user_agent", "2": self.ua},
                # {"1": "Web-Sdk-Ms-Token", "2": ""} # 经测试不带也可以
                {"1": "Web-Sdk-Ms-Token", "2": self.session.cookies.get_dict().get("msToken")}
            ]
            data = {
                "1": 100,
                "2": seqId,
                "3": "1.2.12",
                "4": {},
                "5": 3,
                "6": 0,
                "7": "4e43dcb:Detached: 4e43dcb744e1de020cd0c403f3c12d989b068887", # 经测试不带也可以，写死的
                "8": {
                    "100": {
                        "1": chat_info, # "0:1:发送人user_id:接收user_id"，0,1应该是标记发送和接收
                        "2": 1,
                        "3": int(chat_id), # 事实上即使删除聊天，下次和一样的用户这个 id 也是一样的，所以每次请求 /create 也行
                        "4": json.dumps({
                            "aweType": 800,
                            "itemId": itemId,
                            "uid": author["id"],
                            "secUID": author["secUid"],
                            "content_thumb": { # 视频地址信息
                                "url_list": [author["avatarThumb"]],
                                "uri": author["avatarThumb"]
                            },
                            "content_name": author["nickname"],
                            "cover_url": {
                                "url_list": [video["cover"]],
                                "uri": video["cover"]
                            },
                            "cover_width": video["width"],
                            "cover_height": video["height"]
                        }, separators=(",", ":")),
                        "5": [
                            {"1": "s:mentioned_users", "2": {}},
                            {"1": "s:client_message_id", "2-1": deprecated_uuid}
                        ],
                        "6": 8,
                        "7": "deprecated",
                        "8": deprecated_uuid
                    }
                },
                "9": self.device_id,
                "11": "android" if use_app else "web",
                # "15": env_list,
                # "18": 1
            }
            typedef = {
                '1': {'type': 'int'}, 
                '2': {'type': 'int'}, 
                '3': {'type': 'string'}, 
                '4': {'message_typedef': {}, 'type': 'message'}, 
                '5': {'type': 'int'}, 
                '6': {'type': 'int'}, 
                '7': {'type': 'string'}, 
                '8': {'field_order': ['100'], 'message_typedef': {'100': {'field_order': ['1', '2', '3', '4', '5', '5', '6', '7', '8'], 'message_typedef': {'1': {'type': 'string'}, '2': {'type': 'int'}, '3': {'type': 'int'}, '4': {'type': 'string'}, '5': {'seen_repeated': True, 'field_order': ['1', '2'], 'message_typedef': {'1': {'type': 'string'}, '2': {'message_typedef': {}, 'type': 'message', 'alt_typedefs': {'1': 'string'}}}, 'type': 'message'}, '6': {'type': 'int'}, '7': {'type': 'string'}, '8': {'type': 'string'}}, 'type': 'message'}}, 'type': 'message'}, 
                '9': {'type': 'string'}, 
                '11': {'type': 'string'}, 
                # '15': {'seen_repeated': True, 'field_order': ['1', '2'], 'message_typedef': {'1': {'type': 'string'}, '2': {'type': 'string'}}, 'type': 'message'}, 
                # '18': {'type': 'int'}
            }
            return {"status": 1, "data": {"text": f"{self.unique_id} 创建分享视频内层 data 成功", "response_data": {
                "inner_data": data, "inner_typedef": typedef,
                "seqId": seqId, "env_list": env_list
            }}, "login_expired": 0, "chat_id": chat_id}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 创建分享视频内层 data 出错：{e}", "response_data": ""}, "login_expired": 0, "chat_id": chat_id}

    # 构造私信分享视频 data
    def create_share_video_data(self, chat_info="", chat_id=0, video_info={}, use_app=False):
        try:
            public_share_video_data_result = self.get_public_share_video_data(chat_info=chat_info, chat_id=chat_id, video_info=video_info, use_app=use_app)
            if not public_share_video_data_result.get("status"):
                return public_share_video_data_result
            
            public_share_video_data = public_share_video_data_result.get("data", {}).get("response_data", {})
            data = public_share_video_data["inner_data"]
            typedef = public_share_video_data["inner_typedef"]
            data = blackboxprotobuf.encode_message(data, typedef)
            return {"status": 1, "data": {"text": f'{self.unique_id} 发送私信 http 分享视频 data 编码成功', "response_data": data}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f'{self.unique_id} 发送私信 http 分享视频 data 编码出错：{e}', "response_data": ""}, "login_expired": 0}

    # 发送私信参数检查与填充
    def check_message_send_params(self, 
        receiver_username="", receiver_userid="", 
        message_text="", 
        recommend_emoji=False, emoji_keyword="", emoji_info={}, 
        link_info={}, 
        share_video_info={}, 
        share_place_info={},
        message_data=b'', 
        chat_info="", chat_id=0
    ):
        try:
            if (not message_text) and (not message_data) and (not recommend_emoji) and (not emoji_keyword) and (not emoji_info) and (not link_info) and (not share_video_info) and (not share_place_info):
                return {"status": 0, "data": {"text": f"{self.unique_id} 未提供需要发送的私信内容", "response_data": ""}, "login_expired": 0}
            
            if (not receiver_username) and (not receiver_userid):
                return {"status": 0, "data": {"text": f"{self.unique_id} 未提供需要发送的私信的对象", "response_data": ""}, "login_expired": 0}
            
            if (not chat_info) or (not chat_id):
                if not receiver_userid:
                    follow_user_info_dict = {
                        "username": receiver_username
                    }
                    fill_follow_user_info_result = self.fill_follow_user_info(follow_user_info_dict, max_search_page=1, error_text="未提供需要私信的唯一用户名")
                    if not fill_follow_user_info_result.get("status"):
                        return fill_follow_user_info_result
                    follow_user_info_dict = fill_follow_user_info_result.get("data", {}).get("response_data", {})
                    receiver_userid = follow_user_info_dict.get("user_id")

                get_creation_id_result = self.get_chat_id(receiver_username=receiver_username, receiver_userid=receiver_userid)
                if not get_creation_id_result.get("status"):
                    return get_creation_id_result

                chat_info = get_creation_id_result.get("data", {}).get("response_data", {}).get("chat_info", "")
                chat_id = get_creation_id_result.get("data", {}).get("response_data", {}).get("chat_id", "")
            return {"status": 1, "data": {"text": f"{self.unique_id} 发送私信参数检查完成", "response_data": {"chat_info": chat_info, "chat_id": chat_id}}, "login_expired": 0}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 发送私信参数检查出错：{e}", "response_data": ""}, "login_expired": 0}

    # 发送单条私信
    def tk_message_send_flow(self, 
        receiver_username="", receiver_userid="", 
        message_text="", 
        recommend_emoji=False, emoji_keyword="", emoji_info={}, 
        link_info={}, 
        share_video_info={}, 
        share_place_info={}, 
        message_data=b'', 
        chat_info="", chat_id=0, 
        use_app=False
    ):
        try:
            # 已经编码好的私信内容，最高优先级
            if message_data:
                if use_app:
                    return self.app_message_send(receiver_username=receiver_username, message_data=message_data, chat_info=chat_info, chat_id=chat_id, X_TT_Token=use_app)
                else:
                    return self.web_message_send(receiver_username=receiver_username, message_data=message_data, chat_info=chat_info, chat_id=chat_id)

            check_message_send_params_result = self.check_message_send_params(receiver_username=receiver_username, receiver_userid=receiver_userid, message_text=message_text, recommend_emoji=recommend_emoji, emoji_keyword=emoji_keyword, emoji_info=emoji_info, message_data=message_data, link_info=link_info, share_video_info=share_video_info, share_place_info=share_place_info, chat_info=chat_info, chat_id=chat_id)
            if not check_message_send_params_result.get("status"):
                return check_message_send_params_result

            if (not chat_info) or (not chat_id):
                chat_info = check_message_send_params_result.get("data", {}).get("response_data", {}).get("chat_info", "")
                chat_id = check_message_send_params_result.get("data", {}).get("response_data", {}).get("chat_id", "")

            # 纯文本
            if message_text:
                if use_app:
                    return self.app_message_send(message_text=message_text, message_data=message_data, chat_info=chat_info, chat_id=chat_id, X_TT_Token=use_app)
                else:
                    return self.web_message_send(receiver_username=receiver_username, message_text=message_text, chat_info=chat_info, chat_id=chat_id)
            # 短链
            elif link_info:
                if not link_info.get("link_url"):
                    get_shorten_url_flow_result = self.get_shorten_url_flow(origin_url=link_info.get("origin_url", ""))
                    if not get_shorten_url_flow_result.get("status"):
                        return get_shorten_url_flow_result
                    link_info["link_url"] = get_shorten_url_flow_result.get("data", {}).get("response_data", "")

                create_chat_data_result = self.create_link_info_data(receiver_username=receiver_username, link_info=link_info, chat_info=chat_info, chat_id=chat_id, use_app=use_app)
                if not create_chat_data_result.get("status"):
                    create_chat_data_result["chat_id"] = chat_id # 返回后端包含 chat_id
                    return create_chat_data_result
                message_data = create_chat_data_result.get("data", {}).get("response_data", b'')
                if use_app:
                    message_send_result =  self.app_message_send(message_data=message_data, chat_info=chat_info, chat_id=chat_id, X_TT_Token=use_app)
                else:
                    message_send_result = self.web_message_send(receiver_username=receiver_username, message_data=message_data, chat_info=chat_info, chat_id=chat_id)
                message_send_result["data"]["text"] = message_send_result.get("data", {}).get("text", "").replace("私信", f'短链 {link_info.get("link_url", "")} ')
                return message_send_result
            # 分享视频
            elif share_video_info:
                check_share_videoInfo_result = self.check_share_videoInfo(video_info=share_video_info, chat_id=chat_id)
                if not check_share_videoInfo_result.get("status"):
                    return check_share_videoInfo_result

                video_info = check_share_videoInfo_result.get("data", {}).get("response_data", {})
                create_chat_data_result = self.create_share_video_data(chat_info=chat_info, chat_id=chat_id, video_info=video_info, use_app=use_app)
                if not create_chat_data_result.get("status"):
                    create_chat_data_result["chat_id"] = chat_id # 返回后端包含 chat_id
                    return create_chat_data_result
                message_data = create_chat_data_result.get("data", {}).get("response_data", b'')
                if use_app:
                    message_send_result =  self.app_message_send(message_data=message_data, chat_info=chat_info, chat_id=chat_id, X_TT_Token=use_app)
                else:
                    message_send_result = self.web_message_send(receiver_username=receiver_username, message_data=message_data, chat_info=chat_info, chat_id=chat_id)
                message_send_result["data"]["text"] = message_send_result.get("data", {}).get("text", "").replace("发送私信", f'分享视频 {video_info.get("id")} ')
                return message_send_result
            # 未处理类型
            return {"status": 0, "data": {"text": f"{self.unique_id} 发送私信主流程存在未处理消息类型", "response_data": ""}, "login_expired": 0, "chat_id": chat_id}
        except Exception as e:
            return {"status": 0, "data": {"text": f"{self.unique_id} 发送私信主流程出错：{e}", "response_data": ""}, "login_expired": 0}