import logging
import logging.handlers
import multiprocessing
import random
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Optional
from queue import Queue
from export_interface import *
from manager_base import BaseManager
from utils.shared_manager import set_manager_namespace
from utils.common import PAGINATED_COLLECT_TASKS
import os
import time
class MidManager(BaseManager):
    def __init__(self,
                 main_path="",
                 init_info=None,
                 task_queue=None,
                 upload_queue=None,
                 upload_batch_queue=None,
                 heartbeat_queue=None,
                 log_queue=None,
                 spider_log_queue_dict=None,
                 shared_namespace=None
                 ):
        super().__init__(main_path=main_path, init_info=init_info)

        # 为当前子进程设置共享命名空间
        set_manager_namespace(shared_namespace)
        # if self.redis_url:
        #     self.redis_manager = RedisManager(redis_url=self.redis_url)
        self.task_queue: multiprocessing.Queue = task_queue
        self.upload_queue: multiprocessing.Queue = upload_queue
        self.upload_batch_queue: multiprocessing.Queue = upload_batch_queue
        self.heartbeat_queue: multiprocessing.Queue = heartbeat_queue
        self.create_spider_queue = Queue()
        self.run_spider_quque = Queue()

        self.pid = os.getpid()
        self.logger = self.init_logger(name="manager", log_level=self.log_level, queue=log_queue)
        self.main_pool = ThreadPoolExecutor(max_workers=self.init_info.get("single_process_num", 1))
        self.thread_pool = ThreadPoolExecutor(max_workers=self.init_info.get("single_process_num", 1))

        self.spider_logger_dict: dict[str, logging.Logger] = {}
        for task_type in PAGINATED_COLLECT_TASKS:
            spider_logger = self.init_logger(name=task_type, log_level=self.log_level,
                                             queue=spider_log_queue_dict.get(task_type))
            self.spider_logger_dict[task_type] = spider_logger

    def init_logger(self, name: str, log_level="DEBUG", queue: Optional[multiprocessing.Queue] = None,
                    with_stream=True):
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, log_level.upper(), logging.DEBUG))
        logger.handlers.clear()
        logger.propagate = False
        formatter = logging.Formatter('%(asctime)s - %(filename)s:%(lineno)d - %(levelname)s - %(message)s')
        if with_stream:
            stream_handler = logging.StreamHandler()
            stream_handler.setLevel(logging.NOTSET)
            stream_handler.setFormatter(formatter)
            logger.addHandler(stream_handler)
        if queue:
            queue_handler = logging.handlers.QueueHandler(queue)
            logger.addHandler(queue_handler)
        return logger

    def start(self):
        with ThreadPoolExecutor(max_workers=3) as control_pool:
            control_pool.submit(self.get_tasks)
            control_pool.submit(self.run_spider)
            control_pool.submit(self.send_heartbeat)

    def get_tasks(self):
        try:
            while True:
                if not self.task_queue.empty():
                    all_task_data = self.task_queue.get()
                    task_type = all_task_data.get("task_type")

                    # 根据任务类型路由到不同的处理函数
                    if task_type in PAGINATED_COLLECT_TASKS:
                        self.main_pool.submit(self.run_paginated_task, all_task_data)
                    else:
                        self.main_pool.submit(self.run_all_task, all_task_data)
                time.sleep(30)
        except Exception as e:
            self.logger.error(f"中控 get_tasks 出错：{e}")

    def send_heartbeat(self):
        try:
            while True:
                time.sleep(250)
                self.heartbeat_queue.put({self.pid: time.time()})
        except Exception as e:
            self.logger.error(f"中控 send_heartbeat 出错：{e}")

    def run_spider(self):
        try:
            while True:
                if not self.run_spider_quque.empty():
                    fetch_data, mid_task_data = self.run_spider_quque.get()
                    task_data = mid_task_data["task_data"]
                    task_id = mid_task_data["task_id"]
                    task_type = mid_task_data["task_type"]
                    small_task_id = mid_task_data["small_task_id"]
                    object_id = mid_task_data["object_id"]
                    all_task_data = mid_task_data["all_task_data"]
                    logger: logging.Logger = mid_task_data["logger"]
                    log_context = mid_task_data["log_context"]

                    final_result = do_fetch(**fetch_data)
                    if final_result.get('sec_uid'):
                        task_data['sec_uid'] = final_result['sec_uid']

                    # 任务结束，发送最终状态的回调
                    # 计算正确的 task_status：1->2(成功), 0->3(失败)
                    task_status = 2 if final_result.get("status") == 1 else 3

                    final_upload_data = {
                        "status": final_result.get("status", 0),
                        "task_status": task_status,  # 添加 task_status 字段
                        "not_exist": final_result.get("not_exist", False),
                        "data": final_result.get("data", {}),

                        "task_id": task_id,
                        "task_type": task_type,
                        "small_task_id": small_task_id,
                        "object_id": object_id,
                        "platform": all_task_data.get("platform", ""),
                        "result_list": []  # 用空列表作为结束信号
                    }

                    final_status_log = "成功"
                    if final_result.get("status") == 0:
                        final_status_log = "失败"
                    if final_result.get("not_exist"):
                        final_status_log = "目标不存在"

                    logger.info(f"{log_context} 分页任务处理结束, 状态: {final_status_log}")
                    self.upload_batch_queue.put((final_upload_data, task_type))
                time.sleep(3)
        except Exception as e:
            self.logger.error(f"中控 run_spider 出错：{e}")

    def run_paginated_task(self, all_task_data):
        """
        处理分页任务的入口和调度器。
        遍历所有子任务，并为每个子任务启动一个独立的执行线程。
        """
        task_list = all_task_data.get("details", [])
        if not task_list:
            task_type = all_task_data.get("task_type", "unknown")
            logger = self.spider_logger_dict.get(task_type) or self.logger
            logger.warning(f"任务 {all_task_data.get('task_id')} 收到了空的分页任务列表 (details)，已跳过。")
            return

        for task_data in task_list:
            self.thread_pool.submit(self._execute_single_paginated_task, task_data, all_task_data)

    def _execute_single_paginated_task(self, task_data, all_task_data):
        """
        执行单个分页子任务的核心逻辑。
        这个函数在自己的线程中运行。
        """
        task_type = all_task_data.get("task_type", "")
        task_id = all_task_data.get("task_id")
        small_task_id = task_data.get("small_task_id")
        object_id = task_data.get("object", "")
        log_context = f"[[{task_type}:{task_id}:{small_task_id}:{object_id}]]"
        logger = self.spider_logger_dict[task_type]
        logger.info(f"{log_context} 开始处理分页任务")

        def upload_callback(items_batch):
            # 只发送必要ID和原始数据批次
            consumer_result_dict = {
                "task_id": task_id,
                "small_task_id": small_task_id,
                "object_id": object_id,
                "result_list": items_batch  # 直接使用采集到的项目列表
            }
            logger.debug(f"{log_context} 批处理回调，数量: {len(items_batch)}")
            self.upload_batch_queue.put((consumer_result_dict, task_type))

        init_data = {
            "config": self.init_info,
            "cookies_dict": task_data.get("cookie_dict", {}),
            "proxies": self.proxies
        }

        # 动态构建分页参数，仅在任务数据中存在时传递
        paginated_params = {
        }
        if 'cursor' in task_data and task_data['cursor'] is not None:
            paginated_params['cursor'] = task_data['cursor']
        if 'count' in task_data:
            paginated_params['count'] = task_data['count']
        if 'batch_size' in task_data:
            paginated_params['batch_size'] = task_data['batch_size']
        if 'sec_uid' in task_data and task_data['sec_uid']:
            paginated_params['sec_uid'] = task_data['sec_uid']
        if task_type == 'comment_reply_collect' and 'item_id' in task_data:
            paginated_params['item_id'] = task_data['item_id']

        fetch_data = dict(
            init_data_dict=init_data,
            task_type=task_type,
            object_id=object_id,
            callback=upload_callback,
            task_id=task_id,
            small_task_id=small_task_id,
            **paginated_params
        )
        mid_task_data = dict(
            task_data=task_data, 
            task_id=task_id, 
            task_type=task_type, 
            small_task_id=small_task_id,
            object_id=object_id, 
            all_task_data=all_task_data, 
            logger=logger, 
            log_context=log_context
        )
        self.run_spider_quque.put((fetch_data, mid_task_data))

    def run_all_task(self, all_task_data):
        result_list = []
        result_lock = threading.Lock()
        task_list = all_task_data.get("details", [])
        futures = []

        for task_data in task_list:
            future = self.thread_pool.submit(self.run_single_task, task_data, all_task_data)
            futures.append(future)

        for future in as_completed(futures):
            result = future.result()
            with result_lock:
                result_list.append(result)

        consumer_result_dict = {
            "task_id": all_task_data.get("task_id", 0),
            "task_type": all_task_data.get("task_type", ""),
            "platform": all_task_data.get("platform", ""),
            "result_list": result_list
        }
        self.upload_queue.put((consumer_result_dict, all_task_data.get("task_type", "")))

    def outer_check(self, task_data, all_task_data):
        single_result = {
            "status": 0,
            "data": {"text": "任务调度存在未知分支", "response_data": ""},
            "cookie_dict": task_data.get("cookie_dict", {}),
            "task_id": all_task_data.get("task_id", 0),
            "small_task_id": task_data.get("small_task_id", 0),
            "object_id": task_data.get("object", ""),
            "task_type": all_task_data.get("task_type", 0),
            "platform": all_task_data.get("platform", 0)
        }
        if task_data.get("cookie_dict", {}) is None:
            single_result["data"]["text"] = "该账号 cookie 是 None"
        else:
            task_type = task_data.get("task_type", all_task_data.get("task_type", 0))  # 小任务类型
            update_dict = {
                "task_id": all_task_data.get("task_id", 0),
                "small_task_id": task_data.get("small_task_id", 0),
                "object_id": task_data.get("object", ""),
                "task_type": all_task_data.get("task_type", 0),
                "small_task_type": task_type,
                "platform": all_task_data.get("platform", 0)
            }
            if task_type in [
                "blogger_collect", "video_collect",
                "blogger_fans_collect",  # "博主粉丝采集"
                "blogger_follow_collect",  # "博主关注采集"
                "video_id_collect",  # "视频列表页采集"
                "comment_collect",  # "评论采集"
                "comment_reply_collect",
                "content_publish",
                "private_message",
                "unique_id", "avatar", "nicknames_signatures", "private_account",
                "reply"
            ]:
                return True, update_dict, task_type
            else:
                single_result["data"]["text"] = "无此任务类型"
            single_result.update(update_dict)
        return False, single_result, None

    def run_single_task(self, task_data, all_task_data):
        outer_check_result, update_dict, task_type = self.outer_check(task_data=task_data, all_task_data=all_task_data)
        if not outer_check_result:
            return update_dict

        task_id = all_task_data.get("task_id")
        small_task_id = task_data.get("small_task_id")
        object_id = task_data.get("object", "")
        log_context = f"[[{task_type}:{task_id}:{small_task_id}:{object_id}]]"
        canvas_num = random.randint(**********, **********)
        init_data = {
            "config": self.init_info,
            "unique_id": object_id,
            "cookies_dict": task_data.get("cookie_dict", {}),
            "canvas_num": task_data.get("canvas_num", canvas_num),
            "user_id": task_data.get("user_id", ""),
            "user_sec_uid": task_data.get("user_sec_uid", ""),
            "device_id": task_data.get("device_id", ""),
            "WebIdLastTime": task_data.get("WebIdLastTime", ""),
            "tz_name": task_data.get("tz_name", ""),
            "proxies": task_data.get("proxies", self.proxies),
        }
        if task_type == "content_publish":  # 发作品
            modify_data = task_data.get("modify_data", {})
            init_data.update({
                "delay_publish_time": task_data.get("delay_publish_time", 5),
                "publish_baseurl": self.download_url,
            })
            single_result = do_publish(
                init_data=init_data,
                video_info=modify_data,
                tcm_params={"commerce_toggle_info": {}},  # 默认或 有了 brand_organic_type、branded_content_type
                content_check_id=True,  # 不知道什么东西，默认开启（其实好像有没有这个参数都无所谓）
                video_size=task_data.get("file_size", 0) # 发布的每个视频的字节大小
            )
        elif task_type in ["unique_id", "avatar", "nicknames_signatures", "private_account"]:
            modify_data = task_data.get("modify_data", {})
            single_result = do_modify(
                init_data=init_data,
                task_type=task_type,
                new_unique_id=modify_data.get("new_unique_id", ""),
                profile_img_url=f'{self.download_url}/{modify_data.get("avatarThumb", "")}' if self.download_url else modify_data.get(
                    "avatarThumb", ""),
                new_nickname=modify_data.get("new_nickname", ""),
                signature_text=modify_data.get("signature_text", ""),
                setting_type=task_data.get("setting_type", 0),  # "私密账号" if (setting_type == 1) else "公开账号"
            )
        elif task_type == "private_message":  # 发送私信
            single_result = do_chat(
                init_data=init_data,
                receiver_username=task_data.get("receiver_username", ""),
                receiver_userid=task_data.get("receiver_userid", ""),
                message_text=task_data.get("message_text", ""),
                recommend_emoji=task_data.get("recommend_emoji", False),
                emoji_info=task_data.get("emoji_info", {}),
                emoji_keyword=task_data.get("emoji_keyword", ""),
                link_info=task_data.get("link_info", {}),
                share_video_info=task_data.get("share_video_info", {}),
                share_place_info=task_data.get("share_place_info", {}),
                use_app=task_data.get("use_app", False),
            )
        elif task_type == "reply":  # 发送私信
            single_result = do_follow(
                init_data=init_data,
                author_unique_id=task_data.get("author_unique_id", ""), 
                video_id=task_data.get("video_id", ""), 
                comment_text=task_data.get("comment_text", ""), 
                user_id_map=task_data.get("user_id_map", {}),
                cid=task_data.get("cid", ""), 
                reply_to_reply_id=task_data.get("reply_to_reply_id", "")
            )
        elif task_type in [
            "blogger_collect",  # "博主详情采集"
            "video_collect",  # "视频详情采集"
        ]:
            single_result = do_fetch(
                init_data_dict=init_data,
                task_type=task_type,
                object_id=object_id
            )
            if single_result.get("status") == 1:
                single_result["status"] = 2
                self.logger.info(f"{log_context} 采集任务成功")
            else:
                single_result["status"] = 3
                self.logger.error(f"{log_context} 采集任务失败: {single_result.get('data', {}).get('text')}")
        single_result.update(update_dict)
        return single_result


def run_mid_manager(
        main_path="",
        init_info=None,
        task_queue=None,
        upload_queue=None,
        upload_batch_queue=None,
        heartbeat_queue=None,
        log_queue=None,
        spider_log_queue_dict=None,
        shared_namespace=None
):
    MidManager(
        main_path=main_path,
        init_info=init_info,
        task_queue=task_queue,
        upload_queue=upload_queue,
        upload_batch_queue=upload_batch_queue,
        heartbeat_queue=heartbeat_queue,
        log_queue=log_queue,
        spider_log_queue_dict=spider_log_queue_dict,
        shared_namespace=shared_namespace
    ).start()
