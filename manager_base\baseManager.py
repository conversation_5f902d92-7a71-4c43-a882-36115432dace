from utils import ReqBase

class BaseManager(ReqBase):
    def __init__(self, main_path="", init_info=None):
        self.init_info = init_info
        self.headers = {"Authorization": init_info.get('auto', ''), "Content-Type": "application/json"}
        task_host = self.init_info.get('task_host', '')
        self.download_url = self.init_info.get("inner_files_url", "") # 获取内部资源文件（图片/视频）地址
        self.task_url = task_host + "/task/callbackExe" # 获取任务地址
        self.upload_url = task_host + "/task/callbackUpdate" # 回调地址
        self.proxy_url = self.init_info.get("proxy_url", None)
        self.init_info["PROXY_ENABLED"] = bool(self.proxy_url)
        self.redis_manager = None
        self.logger = None
        self.log_level = init_info.get('log_level', '').upper()
        proxies = {"http": self.proxy_url, "https": self.proxy_url} if self.proxy_url else None
        self.redis_config = self.init_info.get("redis_config", None)
        if self.redis_config["password"]:
            redis_url = f'redis://:{self.redis_config["password"]}@{self.redis_config["host"]}:{self.redis_config["port"]}/{self.redis_config["db"]}'
        else:
            redis_url = f'redis://{self.redis_config["host"]}:{self.redis_config["port"]}/{self.redis_config["db"]}'
        super().__init__(config=self.init_info, main_path=main_path, proxies=proxies, redis_url=redis_url)