import magic, regex, re, json
from typing import List, Dict

PAGINATED_COLLECT_TASKS = [
    "blogger_fans_collect",
    "blogger_follow_collect",
    "video_id_collect",
    "comment_collect",
    "comment_reply_collect",
]

def guess_content_type(byte_data: bytes) -> str:
    try:
        max_byte_index = 2048
        if len(byte_data) < max_byte_index:
            max_byte_index = len(byte_data)
        mime = magic.Magic(mime=True)
        mime_type = mime.from_buffer(byte_data[:max_byte_index])
        return mime_type
    except Exception as e:
        return ""
    
def extract_nested_objects(text: str, key: str="", re_rule: str="") -> List[dict]:
    if re_rule:
        return re.findall(re_rule, text, re.S)
    escaped_key = regex.escape(key)
    pattern = rf'"{escaped_key}"\s*:\s*(\{{(?:[^{{}}]+|(?1))*\}})'
    result = regex.findall(pattern, text)
    result = [json.loads(it) for it in result]
    return result[0] if len(result) == 1 else result

def fix_mentions(text: str):
    i = 0
    length = len(text)
    result = ""
    while i < length:
        char = text[i]
        if char in ('@', '#'):
            start = i
            i += 1
            while i < length and text[i] not in ('@', '#', ' '):
                i += 1
            segment = text[start:i]
            next_char_is_space = (i < length and text[i] == ' ')
            if not next_char_is_space:
                segment += ' '
            result += segment
        else:
            result += char
            i += 1
    return result

def utf16_index_map(text: str):
    utf16_bytes = text.encode('utf-16-le')

    mapping = []
    byte_idx = 0
    while byte_idx < len(utf16_bytes):
        unit1 = int.from_bytes(utf16_bytes[byte_idx:byte_idx+2], 'little')
        byte_idx += 2
        if 0xD800 <= unit1 <= 0xDBFF and byte_idx + 2 <= len(utf16_bytes):
            unit2 = int.from_bytes(utf16_bytes[byte_idx:byte_idx+2], 'little')
            if 0xDC00 <= unit2 <= 0xDFFF:
                byte_idx += 2
        mapping.append(byte_idx // 2)
    return mapping

def parse_single_post_feature_info_text(text: str, user_id_map: Dict[str, str]={}):
    text_extra = []
    markup_text = ""
    tag_id = 0
    i = 0
    length = len(text)
    utf16_map = utf16_index_map(text)

    while i < length:
        char = text[i]

        if char == '@':
            matched = False
            for username in sorted(user_id_map.keys(), key=len, reverse=True):
                mention = '@' + username
                end_pos = i + len(mention)
                if text[i:end_pos] == mention:
                    if end_pos == length or text[end_pos] == ' ':
                        start_utf16 = utf16_map[i - 1] if i > 0 else 0
                        end_utf16 = utf16_map[end_pos - 1]
                        tag_data = {
                            "tag_id": str(tag_id),
                            "start": start_utf16,
                            "end": end_utf16,
                            "user_id": user_id_map[username],
                            "type": 0,
                            "hashtag_name": ""
                        }
                        text_extra.append(tag_data)
                        markup_text += f'<m id="{tag_id}">@{username}</m>'
                        tag_id += 1
                        i = end_pos
                        matched = True
                        break
            if not matched:
                markup_text += char
                i += 1

        elif char == '#':
            j = i + 1
            while j < length and (text[j].isalnum() or text[j] == '_'):
                j += 1
            hashtag = text[i + 1:j]
            if hashtag:
                start_utf16 = utf16_map[i - 1] if i > 0 else 0
                end_utf16 = utf16_map[j - 1]
                tag_data = {
                    "tag_id": str(tag_id),
                    "start": start_utf16,
                    "end": end_utf16,
                    "user_id": "",
                    "type": 1,
                    "hashtag_name": hashtag
                }
                text_extra.append(tag_data)
                markup_text += f'<h id="{tag_id}">#{hashtag}</h>'
                tag_id += 1
                i = j
            else:
                markup_text += char
                i += 1
        else:
            markup_text += char
            i += 1

    return {
        "text": text,
        "text_extra": text_extra,
        "markup_text": markup_text,
    }