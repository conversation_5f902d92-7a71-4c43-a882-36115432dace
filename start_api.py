#!/usr/bin/env python3
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

if __name__ == "__main__":
    import uvicorn
    
    reload = os.getenv("RELOAD", "false").lower() == "true"
    port = int(os.getenv("PORT", "8000"))
    
    uvicorn.run("api.app:app", host="0.0.0.0", port=port, reload=reload)
