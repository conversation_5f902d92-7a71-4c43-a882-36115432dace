---
description: 
globs: 
alwaysApply: false
---
# Git 协同与提交规范

为了打造智能、高效的 Git 协同工作流，提升代码库的可读性与可追溯性，并充分利用 AI 辅助能力，所有团队成员都应遵循此规范。

## 一、核心理念：AI 驱动的 Git 工作流

我们推荐并鼓励使用 Cursor 的 AI 功能来辅助完成以下任务：

*   **一键生成提交信息**：在源代码管理面板，使用"魔法棒"图标 (`Ctrl+M` / `Cmd+M`) 自动生成符合规范的提交信息。
*   **智能化代码审查**：利用 AI 对代码变更进行全面、客观的审查。
*   **辅助解决合并冲突**：在面对复杂冲突时，请求 AI 分析并提供解决方案。

## 二、分支命名规范

清晰的分支命名是高效协作的起点。所有分支都应基于 `dev` 分支创建，并遵循以下格式：

*   **新功能开发**: `feature/<模块>-<功能简述>`
    *   示例: `feature/publish-video-flow`
*   **Bug 修复**: `fix/<模块>-<问题简述>`
    *   示例: `fix/manager-callback-error`
*   **紧急热修复**: `hotfix/<问题简述>`
    *   示例: `hotfix/csrf-token-issue`

## 三、提交规范

### 1. 结构

所有提交信息必须遵循"约定式提交 (Conventional Commits)"格式。

```
<类型>(<可选的作用域>): <标题>

<空一行>

[可选] 本次变更的详细说明，解释背景、动机和实现思路。

[可选] 一个有序列表，逐条说明关键的变更点：
1.  变更点一 (例如, 影响的模块或文件): 具体说明。
2.  变更点二: ...
```

### 2. 类型 (Type)

必须是以下关键字之一：

*   **feat**: 新增功能。
*   **fix**: 修复 Bug。
*   **test**: 新增或修改测试用例。
*   **refactor**: 代码重构，既不新增功能也未修复 Bug。
*   **docs**: 仅修改文档。
*   **style**: 调整代码格式，不影响代码逻辑。
*   **chore**: 构建流程、辅助工具的变更。

### 3. 作用域 (Scope)

可选。用于说明本次提交影响的范围，如模块名 (`manager`, `fetch_flow`)。

### 4. 示例

```
fix(manager): 修复任务回调时数据重复序列化的问题

在 `write_upload_queue` 方法中，当回调数据已经是 JSON 字符串时，`requests` 库的 `json` 参数会进行不必要的二次序列化，导致服务端解析失败。

1. 回调逻辑 (manager.py): 调整了 `_send_callback` 函数，确保在调用 `requests.post` 前，数据为 Python 字典格式。
2. 日志增强: 为回调失败的情况添加了更详细的错误日志，方便快速定位问题。
```

## 四、代码审查 (Code Review)

在提交合并请求 (Pull Request) 前，建议使用 AI 进行一次预审查。这有助于发现潜在问题，统一代码标准。

**审查提示词模板**:

```
请对以下代码变更进行全面审查。

代码变更：
[粘贴 git diff 内容或 PR 链接]

请从以下角度分析：
1. 潜在的 Bug 和逻辑错误。
2. 性能优化建议。
3. 代码规范问题 (是否遵循项目规范)。
4. 可读性与可维护性。
5. 架构设计合理性。

请以列表形式输出审查结果，并按 (高、中、低) 标记问题等级。
```

## 五、合并策略 (Merge Strategy)

为保持 `main` 和 `dev` 分支的历史记录清晰，我们采用以下策略：

*   **功能分支合并到 `dev`**: 优先使用 **Squash and Merge**。将一个功能分支的所有提交合并成一个，写入 `dev` 分支，并附上标准化的提交信息。
*   **`dev` 分支合并到 `main`**: 在发布新版本时，使用 **Merge Commit**。这会保留 `dev` 分支的完整开发历史，并创建一个清晰的合并节点。



本次操作，不能操作我的git！！！


