2025-07-11 10:54:29,941 - manager.py:376 - INFO - 
[系统初始化]
任务类型: ['user_info_modify', 'private_message', 'content_publish', 'blogger_collect', 'video_collect', 'blogger_interaction', 'video_id_collect', 'blogger_follow_collect', 'blogger_fans_collect', 'comment_collect']
进程数: 12 | 线程数: 600
进程超时: 3600秒 | 请求超时: 30秒
2025-07-11 10:54:29,942 - manager.py:53 - INFO - 开始执行更新 ms_token 的定时任务...
2025-07-11 10:54:29,942 - manager.py:386 - INFO - Token 定时更新线程已启动，将每24小时执行一次。
2025-07-11 10:54:30,030 - manager.py:315 - DEBUG - [心跳] 活跃进程: 12
2025-07-11 10:54:32,445 - manager.py:122 - DEBUG - 获取     blogger_collect     任务接口响应：{'data': [], 'msg': '成功', 're_code': 0, 'success': True}
2025-07-11 10:54:35,848 - manager.py:122 - DEBUG - 获取  blogger_follow_collect 任务接口响应：{'data': [], 'msg': '成功', 're_code': 0, 'success': True}
2025-07-11 10:54:37,594 - manager.py:122 - DEBUG - 获取     user_info_modify    任务接口响应：{'data': [], 'msg': '成功', 're_code': 0, 'success': True}
2025-07-11 10:54:44,024 - manager.py:122 - DEBUG - 获取     content_publish     任务接口响应：{'data': [], 'msg': '成功', 're_code': 0, 'success': True}
2025-07-11 10:54:46,393 - manager.py:122 - DEBUG - 获取   blogger_interaction   任务接口响应：{'data': [], 'msg': '成功', 're_code': 0, 'success': True}
2025-07-11 10:54:49,128 - manager.py:70 - ERROR - [msToken] 更新异常 | 详情: SOCKSHTTPSConnectionPool(host='mssdk-ttp2.tiktokw.us', port=443): Max retries exceeded with url: /web/report?msToken=DpKsIPv700H7J0uMan6KSmLk_NFEjjCbPXgzD157s0DMTEMRkqiPjmxYVhaRPM7PnjkhYTs_0l_Z8nzJUfI3plQcGMKoyXODU8msJs3egC-y3rsLczXess6Fdjae5xcOi8guzbN69JM%3D&X-Bogus=DFSzswVLFO2ANelxCSwV7NzDOl8x&X-Gnarly=M%2FadM%2FbLnr9VHxzeNf225-LOXGZQ4v1BG5973m0HWG80Nt%2FjU5uMq84vlEwvIcQXKZgRUtLoMH6mshYz888C0PETHhyulqNpDyNNMK9s58GXFQoz1ziaMFpBosdjtp4hY8fWgDlHVdI5NNkX6O8f4F1t5M%2FliXYwiM98GeqtAzccow3bHEQ-BW1E1kVC-U5pBoQw-lRm638Pru9dRsblpfFriATOcgt8zjun38qUE419n0iAxVJ7bh7TIUg7m2nKe6CtHM16Y%2FSb (Caused by NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x7be9bffd1dc0>: Failed to establish a new connection: Connection closed unexpectedly'))
Traceback (most recent call last):
  File "/opt/venv/lib/python3.12/site-packages/socks.py", line 809, in connect
    negotiate(self, dest_addr, dest_port)
  File "/opt/venv/lib/python3.12/site-packages/socks.py", line 443, in _negotiate_SOCKS5
    self.proxy_peername, self.proxy_sockname = self._SOCKS5_request(
                                               ^^^^^^^^^^^^^^^^^^^^^
  File "/opt/venv/lib/python3.12/site-packages/socks.py", line 470, in _SOCKS5_request
    chosen_auth = self._readall(reader, 2)
                  ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/venv/lib/python3.12/site-packages/socks.py", line 278, in _readall
    raise GeneralProxyError("Connection closed unexpectedly")
socks.GeneralProxyError: Connection closed unexpectedly

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/venv/lib/python3.12/site-packages/urllib3/contrib/socks.py", line 110, in _new_conn
    conn = socks.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/venv/lib/python3.12/site-packages/socks.py", line 209, in create_connection
    raise err
  File "/opt/venv/lib/python3.12/site-packages/socks.py", line 199, in create_connection
    sock.connect((remote_host, remote_port))
  File "/opt/venv/lib/python3.12/site-packages/socks.py", line 47, in wrapper
    return function(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/venv/lib/python3.12/site-packages/socks.py", line 814, in connect
    raise GeneralProxyError("Socket error", error)
socks.GeneralProxyError: Socket error: Connection closed unexpectedly

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "/opt/venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 488, in _make_request
    raise new_e
  File "/opt/venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "/opt/venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "/opt/venv/lib/python3.12/site-packages/urllib3/connection.py", line 753, in connect
    self.sock = sock = self._new_conn()
                       ^^^^^^^^^^^^^^^^
  File "/opt/venv/lib/python3.12/site-packages/urllib3/contrib/socks.py", line 141, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x7be9bffd1dc0>: Failed to establish a new connection: Connection closed unexpectedly

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/opt/venv/lib/python3.12/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "/opt/venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "/opt/venv/lib/python3.12/site-packages/urllib3/util/retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: SOCKSHTTPSConnectionPool(host='mssdk-ttp2.tiktokw.us', port=443): Max retries exceeded with url: /web/report?msToken=DpKsIPv700H7J0uMan6KSmLk_NFEjjCbPXgzD157s0DMTEMRkqiPjmxYVhaRPM7PnjkhYTs_0l_Z8nzJUfI3plQcGMKoyXODU8msJs3egC-y3rsLczXess6Fdjae5xcOi8guzbN69JM%3D&X-Bogus=DFSzswVLFO2ANelxCSwV7NzDOl8x&X-Gnarly=M%2FadM%2FbLnr9VHxzeNf225-LOXGZQ4v1BG5973m0HWG80Nt%2FjU5uMq84vlEwvIcQXKZgRUtLoMH6mshYz888C0PETHhyulqNpDyNNMK9s58GXFQoz1ziaMFpBosdjtp4hY8fWgDlHVdI5NNkX6O8f4F1t5M%2FliXYwiM98GeqtAzccow3bHEQ-BW1E1kVC-U5pBoQw-lRm638Pru9dRsblpfFriATOcgt8zjun38qUE419n0iAxVJ7bh7TIUg7m2nKe6CtHM16Y%2FSb (Caused by NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x7be9bffd1dc0>: Failed to establish a new connection: Connection closed unexpectedly'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/app/manager.py", line 59, in _update_token_logic
    token_info = get_tiktok_token(config=self.init_info, token=current_token, proxy=proxy)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/utils/msTokenUtil.py", line 65, in get_tiktok_token
    response = requests.post(
               ^^^^^^^^^^^^^^
  File "/opt/venv/lib/python3.12/site-packages/requests/api.py", line 115, in post
    return request("post", url, data=data, json=json, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/venv/lib/python3.12/site-packages/requests/api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/venv/lib/python3.12/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/venv/lib/python3.12/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/venv/lib/python3.12/site-packages/requests/adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: SOCKSHTTPSConnectionPool(host='mssdk-ttp2.tiktokw.us', port=443): Max retries exceeded with url: /web/report?msToken=DpKsIPv700H7J0uMan6KSmLk_NFEjjCbPXgzD157s0DMTEMRkqiPjmxYVhaRPM7PnjkhYTs_0l_Z8nzJUfI3plQcGMKoyXODU8msJs3egC-y3rsLczXess6Fdjae5xcOi8guzbN69JM%3D&X-Bogus=DFSzswVLFO2ANelxCSwV7NzDOl8x&X-Gnarly=M%2FadM%2FbLnr9VHxzeNf225-LOXGZQ4v1BG5973m0HWG80Nt%2FjU5uMq84vlEwvIcQXKZgRUtLoMH6mshYz888C0PETHhyulqNpDyNNMK9s58GXFQoz1ziaMFpBosdjtp4hY8fWgDlHVdI5NNkX6O8f4F1t5M%2FliXYwiM98GeqtAzccow3bHEQ-BW1E1kVC-U5pBoQw-lRm638Pru9dRsblpfFriATOcgt8zjun38qUE419n0iAxVJ7bh7TIUg7m2nKe6CtHM16Y%2FSb (Caused by NewConnectionError('<urllib3.contrib.socks.SOCKSHTTPSConnection object at 0x7be9bffd1dc0>: Failed to establish a new connection: Connection closed unexpectedly'))
2025-07-11 10:54:56,797 - manager.py:122 - DEBUG - 获取     private_message     任务接口响应：{'data': [], 'msg': '成功', 're_code': 0, 'success': True}
2025-07-11 10:59:13,355 - manager.py:376 - INFO - 
[系统初始化]
任务类型: ['user_info_modify', 'private_message', 'content_publish', 'blogger_collect', 'video_collect', 'blogger_interaction', 'video_id_collect', 'blogger_follow_collect', 'blogger_fans_collect', 'comment_collect']
进程数: 12 | 线程数: 600
进程超时: 3600秒 | 请求超时: 30秒
2025-07-11 10:59:13,356 - manager.py:53 - INFO - 开始执行更新 ms_token 的定时任务...
2025-07-11 10:59:13,356 - manager.py:386 - INFO - Token 定时更新线程已启动，将每24小时执行一次。
2025-07-11 10:59:13,748 - manager.py:122 - DEBUG - 获取     user_info_modify    任务接口响应：{'data': [], 'msg': '成功', 're_code': 0, 'success': True}
2025-07-11 10:59:13,755 - manager.py:122 - DEBUG - 获取     content_publish     任务接口响应：{'data': [], 'msg': '成功', 're_code': 0, 'success': True}
2025-07-11 10:59:13,759 - manager.py:122 - DEBUG - 获取      video_collect      任务接口响应：{'data': [], 'msg': '成功', 're_code': 0, 'success': True}
2025-07-11 10:59:13,762 - manager.py:122 - DEBUG - 获取     private_message     任务接口响应：{'data': [], 'msg': '成功', 're_code': 0, 'success': True}
2025-07-11 10:59:13,765 - manager.py:122 - DEBUG - 获取     blogger_collect     任务接口响应：{'data': [], 'msg': '成功', 're_code': 0, 'success': True}
2025-07-11 10:59:13,770 - manager.py:122 - DEBUG - 获取   blogger_interaction   任务接口响应：{'data': [], 'msg': '成功', 're_code': 0, 'success': True}
2025-07-11 10:59:13,779 - manager.py:122 - DEBUG - 获取     video_id_collect    任务接口响应：{'data': [], 'msg': '成功', 're_code': 0, 'success': True}
2025-07-11 10:59:13,784 - manager.py:122 - DEBUG - 获取     comment_collect     任务接口响应：{'data': [], 'msg': '成功', 're_code': 0, 'success': True}
2025-07-11 10:59:13,785 - manager.py:122 - DEBUG - 获取   blogger_fans_collect  任务接口响应：{'data': [], 'msg': '成功', 're_code': 0, 'success': True}
2025-07-11 10:59:13,789 - manager.py:122 - DEBUG - 获取  blogger_follow_collect 任务接口响应：{'data': [], 'msg': '成功', 're_code': 0, 'success': True}
2025-07-11 10:59:14,703 - manager.py:315 - DEBUG - [心跳] 活跃进程: 12
2025-07-11 10:59:16,488 - manager.py:63 - INFO - 成功获取到新的 ms_token: YfosNOxAifCJ5shO9tpDP_dgVHUVo21fBpxy6jv0c5GH4zvTt4mtOg8fL5CaJ6iivKZv3rqq6orla7TyRXsb2uTtCvtqF3_O1aaNOHR7x0kGSOL7QEGB6AY7yM0n0fdxXAC2DyhEwPc=
2025-07-11 10:59:16,488 - manager.py:66 - INFO - 共享 ms_token 已更新。
2025-07-11 10:59:17,466 - manager.py:436 - DEBUG - 检测到按键退出，程序退出...
2025-07-11 14:10:37,058 - manager.py:376 - INFO - 
[系统初始化]
任务类型: ['user_info_modify', 'private_message', 'content_publish', 'blogger_collect', 'video_collect', 'blogger_interaction', 'video_id_collect', 'blogger_follow_collect', 'blogger_fans_collect', 'comment_collect']
进程数: 12 | 线程数: 600
进程超时: 3600秒 | 请求超时: 30秒
2025-07-11 14:10:37,059 - manager.py:53 - INFO - 开始执行更新 ms_token 的定时任务...
2025-07-11 14:10:37,059 - manager.py:386 - INFO - Token 定时更新线程已启动，将每24小时执行一次。
2025-07-11 14:10:38,562 - manager.py:315 - DEBUG - [心跳] 活跃进程: 12
2025-07-11 14:10:42,133 - manager.py:63 - INFO - 成功获取到新的 ms_token: OWdTKV1WUxKJ62c6VikoktPKhROnvRWroEu3DcV3t9LD7mRpemh4Vz66gQHTrs8SaZs8NjOPusGLcmpmoOqAANbtA3kt1D4lWpJ6bsRlY3keOMfHD6DW4tnFXqyIqqINCM_Zyv420TE=
2025-07-11 14:10:42,134 - manager.py:66 - INFO - 共享 ms_token 已更新。
2025-07-11 14:10:42,198 - manager.py:436 - DEBUG - 检测到按键退出，程序退出...
