import hashlib, json, base64, hmac, math, random, time, magic
from functools import reduce
from urllib.parse import urlencode, urlparse
from typing import Dict, List, Union
from copy import deepcopy

def tk_urlencode_1(data: dict) -> str:
    return urlencode(data).replace("%3D", "").replace("+", "%20")

def tk_urlencode_2(data: dict) -> str:
    return urlencode(data).replace("%3D", "=").replace("+", "%20")

def unsigned_right_shift(a: int, b: int) -> int:
    a = a & 0xFFFFFFFF
    return (a >> b) & 0xFFFFFFFF

def signed_left_shift(a: int, b: int) -> int:
    a = a & 0xFFFFFFFF
    result = (a << b) & 0xFFFFFFFF
    return result if result < 0x80000000 else result - 0x100000000

def signed_right_shift(a: int, b: int) -> int:
    b = b % 32
    a = a & 0xFFFFFFFF
    if a >= 0x80000000:
        result = (a >> b) - (1 << (32 - b))
    else:
        result = a >> b
    return result

def do_xor(a: int, b: int) -> int:
    result = (a ^ b) & 0xFFFFFFFF
    return result if result <= 0x7FFFFFFF else result - 0x100000000

def get_canvas_num(canvas_str: str) -> int:
    t = 3735928559
    for i in range(32):
        t = (65599 * t + ord(canvas_str[t % len(canvas_str)])) & 0xFFFFFFFF
    return t

def guess_content_type(byte_data: bytes) -> str:
    try:
        max_byte_index = 2048
        if len(byte_data) < max_byte_index:
            max_byte_index = len(byte_data)
        mime = magic.Magic(mime=True)
        mime_type = mime.from_buffer(byte_data[:max_byte_index])
        return mime_type
    except Exception as e:
        return ""

def do_base64(message: str) -> str:
    return base64.b64encode(message.encode('latin1')).decode('utf-8')

def do_md5hash(message_byte: bytes) -> str:
    return hashlib.md5(message_byte).hexdigest()

def get_data_bytes(data: Union[str, bytes]) -> list:
    if isinstance(data, str):
        first_data_result = do_md5hash(data.encode("utf-8"))
    else:
        first_data_result = do_md5hash(data)
    data_result = do_md5hash(bytes.fromhex(first_data_result))
    data_bytes_list = list(bytes.fromhex(data_result))
    return data_bytes_list

def str_encode(e: bytes, t: str) -> str:
    n = list(range(256))
    o = 0
    a = ''
    for s in range(256):
        o = (o + n[s] + e[s % len(e)]) % 256
        n[s], n[o] = n[o], n[s]
    u = o = 0
    for l in range(len(t)):
        u = (u + 1) % 256
        o = (o + n[u]) % 256
        n[u], n[o] = n[o], n[u]
        a += chr(ord(t[l]) ^ n[(n[u] + n[o]) % 256])
    return a

def inner_encode(before_enc_str: str, str_key: str="s2") -> str:
    str_dict = {
        "s0": "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",
        "s1": "Dkdpgh4ZKsQB80/Mfvw36XI1R25+WUAlEi7NLboqYTOPuzmFjJnryx9HVGcaStCe=",
        "s2": "Dkdpgh4ZKsQB80/Mfvw36XI1R25-WUAlEi7NLboqYTOPuzmFjJnryx9HVGcaStCe=", # X-Bogus
        "s3": "u09tbS3UvgDEe6r-ZVMXzLpsAohTn7mdINQlW412GqBjfYiyk8JORCF5/xKHwacP=" # X-Gnarly
    }
    char_str = str_dict[str_key]
    enc_str = ''
    before_xbstr_len = len(before_enc_str)
    full_groups = before_xbstr_len // 3 * 3
    for i in range(0, full_groups, 3):
        mid_num = (ord(before_enc_str[i]) & 255) << 16 | (ord(before_enc_str[i+1]) & 255) << 8 | (ord(before_enc_str[i+2]) & 255)
        enc_str += (
            char_str[(mid_num & 16515072) >> 18] +
            char_str[(mid_num & 258048) >> 12] +
            char_str[(mid_num & 4032) >> 6] +
            char_str[mid_num & 63]
        )
    remainder = before_xbstr_len % 3
    if remainder:
        mid_num = (ord(before_enc_str[full_groups]) & 255) << 16
        if remainder == 2:
            mid_num |= (ord(before_enc_str[full_groups + 1]) & 255) << 8
        enc_str += char_str[(mid_num & 16515072) >> 18] + char_str[(mid_num & 258048) >> 12]
        enc_str += char_str[(mid_num & 4032) >> 6] if remainder == 2 else "="
        enc_str += "="
    return enc_str

# X-Bogus
# now_time 10 位时间戳
# extra_num（ubcode）：发布视频 128、关注用户/直播fetch接口/个人信息修改类/采集视频评论 0
def get_XBogus(params: dict={}, data: Union[dict, str]={}, ua: str="", now_time: int=0, canvas_num: int=2010578131, extra_num: int=0, no_json: bool=False) -> str:
    params_bytes = get_data_bytes(tk_urlencode_2(params))
    # data 不是 json 而是正常表单数据的情况，入参 no_json: True
    if isinstance(data, dict):
        if no_json:
            data = tk_urlencode_2(data)
        else:
            data = json.dumps(data, separators=(",", ":"), ensure_ascii=False)
    else:
        data=data
    data_bytes = get_data_bytes(data)

    key = bytes([0, 1, extra_num])
    ua_base64_str = do_base64(str_encode(key, ua))
    ua_enc_result = do_md5hash(ua_base64_str.encode('utf-8'))
    ua_bytes = list(bytes.fromhex(ua_enc_result))
    
    bytes_19 = [(int(byte) & 255) for byte in [64, 0, 1, extra_num]]
    bytes_19.extend([params_bytes[14], params_bytes[15]])
    bytes_19.extend([data_bytes[14], data_bytes[15]])
    bytes_19.extend([ua_bytes[14], ua_bytes[15]])
    time_bytes = [
        (now_time >> 24) & 255,
        (now_time >> 16) & 255,
        (now_time >> 8) & 255,
        (now_time >> 0) & 255
    ]
    bytes_19.extend(time_bytes)
    canvas_bytes = [
        (canvas_num >> 24) & 255,
        (canvas_num >> 16) & 255,
        (canvas_num >> 8) & 255,
        (canvas_num >> 0) & 255
    ]
    bytes_19.extend(canvas_bytes)
    last_byte = reduce(lambda a, b: a ^ b, bytes_19)
    bytes_19.append(last_byte)
    # print(bytes_19)

    bytes_19 = [(int(byte) & 255) for byte in bytes_19]
    before_xb_str = chr(2) + chr(255) + str_encode(chr(255).encode("latin1"), ''.join(chr(x) for x in bytes_19))
    XBogus = inner_encode(before_xb_str)
    return XBogus

def sort_dict(data: Union[Dict, List]) -> Union[Dict, List]:
    if isinstance(data, dict):
        return {key: sort_dict(value) for key, value in sorted(data.items())}
    elif isinstance(data, list):
        return [sort_dict(item) for item in data]
    else:
        return data

def custom_dumps(data: dict) -> str:
    result = json.dumps(data, separators=(',', ':'), ensure_ascii=False)
    result = result.replace('{\\"', '{\"').replace('\\":', '\":').replace('\\"', '"')
    return result

def str_encode_1(e: int, t: str) -> int:
    for char in t:
        e = unsigned_right_shift(65599 * (e ^ ord(char)), 0)
    return e

def str_encode_2(e: int, t: str) -> int:
    r = 0
    while r < len(t):
        n = ord(t[r])
        if 55296 <= n <= 56319 and r + 1 < len(t):
            o = ord(t[r + 1])
            if 56320 == (o & 64512):
                n = ((n & 1023) << 10) + (o & 1023) + 65536
                r += 1
        e = unsigned_right_shift(65599 * e + n, 0)
        r += 1
    return e

def change_midNum(mid_num: int) -> int:
    if mid_num < 26:
        mid_num += 65
    elif mid_num < 52:
        mid_num += 71
    elif mid_num < 62:
        mid_num -= 4
    else:
        mid_num -=17
    return mid_num

def num_to_5str(num: int) -> str:
    r_shift_list = [24, 18, 12, 6, 0]
    ret_str = ""
    for shift in r_shift_list:
        mid_num = (num >> shift) & 63
        ret_str += chr(change_midNum(mid_num))
    return ret_str

# _signature
# params：不包含 X-Bogus 的 params（ X-Bogus 单独传参）
# now_time：10位长度的整数
# new Cg[0](!Ga[0])[Kg[541]](t)[e]()
def get_signature(url: str="", params: dict={}, X_Bogus: str="", data: Union[dict, str]={}, ua: str="", now_time: int=0, location_href: str="", canvas_num: int=2010578131) -> str:
    url_path = urlparse(url).path
    enc_sign_array = ["_02B4Z6wo00001"]
    now_time_enc1_num = str_encode_1(0, str(now_time))

    parse_location_href = urlparse(location_href)
    location_href = parse_location_href.netloc + parse_location_href.path + ('?' + parse_location_href.query if parse_location_href.query else '') + ('#' + parse_location_href.fragment if parse_location_href.fragment else '')
    href_enc1_num = str_encode_1(now_time_enc1_num, location_href)
    second_num = href_enc1_num % 65521
    first_num = unsigned_right_shift(now_time ^ (second_num * 65521), 0)

    binary_str = bin(first_num)[2:]
    if len(binary_str) < 32:
        binary_str = binary_str.zfill(32)
    elif len(binary_str) > 32:
        binary_str = binary_str[-32:]
    binary_str = "10000000110000" + binary_str
    first_num = int(binary_str, 2)
    third_num = str_encode_1(0, str(first_num))

    if data:
        if "WebIdLastTime" in params:
            inner_params = deepcopy(params)
            WebIdLastTime = inner_params["WebIdLastTime"]
            del inner_params["WebIdLastTime"]
            sort_data_str = custom_dumps(sort_dict(data))
            body_hash = str_encode_2(0, sort_data_str)
            sort_params_str = f'body_hash={body_hash}&WebIdLastTime={WebIdLastTime}&X-Bogus={X_Bogus}&{tk_urlencode_1(sort_dict(inner_params))}&pathname={url_path}&tt_webid=&uuid='
        else:
            sort_data_str = custom_dumps(sort_dict(data))
            body_hash = str_encode_2(0, sort_data_str)
            sort_params_str = f'body_hash={body_hash}&X-Bogus={X_Bogus}&{tk_urlencode_1(sort_dict(params))}&pathname={url_path}&tt_webid=&uuid='
    else:
        if "WebIdLastTime" in params:
            inner_params = deepcopy(params)
            WebIdLastTime = inner_params["WebIdLastTime"]
            del inner_params["WebIdLastTime"]
            sort_params_str = f'WebIdLastTime={WebIdLastTime}&X-Bogus={X_Bogus}&{tk_urlencode_1(sort_dict(inner_params))}&pathname={url_path}&tt_webid=&uuid='
        else:
            sort_params_str = f'X-Bogus={X_Bogus}&{tk_urlencode_1(sort_dict(params))}&pathname={url_path}&tt_webid=&uuid='
    detect_num = 1

    first_round_num = signed_right_shift(first_num, 2)
    enc_sign_array.append(num_to_5str(first_round_num))

    before_mid_sec_num2 = unsigned_right_shift(int(first_num / 4294967296), 0)
    mid_sec_num1 = signed_left_shift(first_num, 28)
    mid_sec_num2 = unsigned_right_shift(before_mid_sec_num2, 4)
    second_round_num = mid_sec_num1 | mid_sec_num2
    enc_sign_array.append(num_to_5str(second_round_num))

    before_mid_third_num2 = canvas_num ^ first_num
    mid_third_num1 = signed_left_shift(before_mid_sec_num2, 26)
    mid_third_num2 = unsigned_right_shift(before_mid_third_num2, 6)
    third_round_num = mid_third_num1 | mid_third_num2
    enc_sign_array.append(num_to_5str(third_round_num))
    
    fourth_num = before_mid_third_num2 & 63
    enc_sign_array.append(chr(change_midNum(fourth_num)))
    ua_num = signed_left_shift(str_encode_1(third_num, ua) % 65521, 16)
    params_num = str_encode_1(third_num, sort_params_str) % 65521
    mid_fifth_round_num = ua_num | params_num
    fifth_round_num = signed_right_shift(mid_fifth_round_num, 2)
    enc_sign_array.append(num_to_5str(fifth_round_num))

    mid_detect_num = signed_left_shift(detect_num, 8)
    _feVersion = 2
    mid_detect_feVersion = mid_detect_num | signed_left_shift(_feVersion, 4)

    mid_sixth_round_num = mid_detect_feVersion ^ first_num
    mid_sixth_num1 = signed_left_shift(mid_fifth_round_num, 28)
    mid_sixth_num2 = unsigned_right_shift(mid_sixth_round_num, 4)
    sixth_round_num = mid_sixth_num1 | mid_sixth_num2
    enc_sign_array.append(num_to_5str(sixth_round_num))
    enc_sign_array.append(num_to_5str(second_num))

    mid_sign_num = str_encode_2(0, "".join(enc_sign_array))
    enc_sign_array.append(hex(mid_sign_num)[2:][-2:])
    result = "".join(enc_sign_array)
    return result

def hmac_sha256(key: bytes, message: str) -> bytes:
    return hmac.new(key, message.encode('utf-8'), hashlib.sha256)

def do_sha256hash(message: str) -> str:
    return hashlib.sha256(message.encode("utf-8")).hexdigest()

def get_hmac_key(secret_access_key: str, date: str, region: str, service: str) -> bytes:
    key = f"AWS4{secret_access_key}".encode('utf-8')
    update_date = hmac_sha256(key, date).digest()
    update_region = hmac_sha256(update_date, region).digest()
    update_service = hmac_sha256(update_region, service).digest()
    hmac_key = hmac_sha256(update_service, "aws4_request").digest()
    return hmac_key

# authorization 中的 Signature
def get_Signature(secret_key: str, session_token: str, req_method: str, url_path: str, params: dict, req_body: Union[dict, str] = "", current_time: str="") -> str:
    hmac_key = get_hmac_key(secret_key, current_time[0:8], "US-TTP", "vod")
    inner_text = f'{req_method}\n{url_path}\n{urlencode(params)}\nx-amz-date:{current_time}\nx-amz-security-token:{session_token}\n\nx-amz-date;x-amz-security-token\n{do_sha256hash(req_body)}'
    message = f"AWS4-HMAC-SHA256\n{current_time}\n{current_time[0:8]}/US-TTP/vod/aws4_request\n{do_sha256hash(inner_text)}"
    signature = hmac_sha256(hmac_key, message).hexdigest()
    return signature

def get_CRC32(start_index: int, block_size: int, file_data: bytes) -> str:
    t1 = time.time()
    table_list = [0, 1996959894, 3993919788, 2567524794, 124634137, 1886057615, 3915621685, 2657392035, 249268274, 2044508324, 3772115230, 2547177864, 162941995, 2125561021, 3887607047, 2428444049, 498536548, 1789927666, 4089016648, 2227061214, 450548861, 1843258603, 4107580753, 2211677639, 325883990, 1684777152, 4251122042, 2321926636, 335633487, 1661365465, 4195302755, 2366115317, 997073096, 1281953886, 3579855332, 2724688242, 1006888145, 1258607687, 3524101629, 2768942443, 901097722, 1119000684, 3686517206, 2898065728, 853044451, 1172266101, 3705015759, 2882616665, 651767980, 1373503546, 3369554304, 3218104598, 565507253, 1454621731, 3485111705, 3099436303, 671266974, 1594198024, 3322730930, 2970347812, 795835527, 1483230225, 3244367275, 3060149565, 1994146192, 31158534, 2563907772, 4023717930, 1907459465, 112637215, 2680153253, 3904427059, 2013776290, 251722036, 2517215374, 3775830040, 2137656763, 141376813, 2439277719, 3865271297, 1802195444, 476864866, 2238001368, 4066508878, 1812370925, 453092731, 2181625025, 4111451223, 1706088902, 314042704, 2344532202, 4240017532, 1658658271, 366619977, 2362670323, 4224994405, 1303535960, 984961486, 2747007092, 3569037538, 1256170817, 1037604311, 2765210733, 3554079995, 1131014506, 879679996, 2909243462, 3663771856, 1141124467, 855842277, 2852801631, 3708648649, 1342533948, 654459306, 3188396048, 3373015174, 1466479909, 544179635, 3110523913, 3462522015, 1591671054, 702138776, 2966460450, 3352799412, 1504918807, 783551873, 3082640443, 3233442989, 3988292384, 2596254646, 62317068, 1957810842, 3939845945, 2647816111, 81470997, 1943803523, 3814918930, 2489596804, 225274430, 2053790376, 3826175755, 2466906013, 167816743, 2097651377, 4027552580, 2265490386, 503444072, 1762050814, 4150417245, 2154129355, 426522225, 1852507879, 4275313526, 2312317920, 282753626, 1742555852, 4189708143, 2394877945, 397917763, 1622183637, 3604390888, 2714866558, 953729732, 1340076626, 3518719985, 2797360999, 1068828381, 1219638859, 3624741850, 2936675148, 906185462, 1090812512, 3747672003, 2825379669, 829329135, 1181335161, 3412177804, 3160834842, 628085408, 1382605366, 3423369109, 3138078467, 570562233, 1426400815, 3317316542, 2998733608, 733239954, 1555261956, 3268935591, 3050360625, 752459403, 1541320221, 2607071920, 3965973030, 1969922972, 40735498, 2617837225, 3943577151, 1913087877, 83908371, 2512341634, 3803740692, 2075208622, 213261112, 2463272603, 3855990285, 2094854071, 198958881, 2262029012, 4057260610, 1759359992, 534414190, 2176718541, 4139329115, 1873836001, 414664567, 2282248934, 4279200368, 1711684554, 285281116, 2405801727, 4167216745, 1634467795, 376229701, 2685067896, 3608007406, 1308918612, 956543938, 2808555105, 3495958263, 1231636301, 1047427035, 2932959818, 3654703836, 1088359270, 936918e3, 2847714899, 3736837829, 1202900863, 817233897, 3183342108, 3401237130, 1404277552, 615818150, 3134207493, 3453421203, 1423857449, 601450431, 3009837614, 3294710456, 1567103746, 711928724, 3020668471, 3272380065, 1510334235, 755167117]
    table_list = [int(x) for x in table_list]
    crc = 0xFFFFFFFF
    data_block = file_data[start_index: start_index + block_size]
    for byte in data_block:
        byte = int(byte)
        crc = table_list[255 & (crc ^ byte)] ^ ((crc >> 8) & 0xFFFFFFFF)
    result = format(crc ^ 0xFFFFFFFF, '08x')
    print(f"运算 CRC32 的值：{result}，用时 {time.time() - t1} s")
    return result

class CustomRandom(object):
    def __init__(self, timestamp: int=0, test_hd: list=[], gd: int=1):
        if not timestamp: # 13 位
            timestamp = int(time.time() * 1000)

        if test_hd: # 方便调试用
            self.hd = test_hd
        else:
            self.hd = [2517678443,2718276124,3212677781,2633865432,217618912,2931180889,1498001188,2157053261,211147047,185100057,2903579748,3732962506, 4294967295 & timestamp, math.floor(4294967296 * random.random()), math.floor(4294967296 * random.random()), math.floor(4294967296 * random.random())]
        self.gd = gd

    def custom_rand(self):
        e = self.get_seed(self.hd, 8)
        t = e[self.gd]
        r = unsigned_right_shift(4294965248 & e[self.gd + 8], 11)
        if 7 == self.gd:
            self.hd[12] = self.hd[12] + 1 & 4294967295
            self.gd = 0
        else:
            self.gd += 1
        return (t + 4294967296 * r) / math.pow(2, 53)
    
    def get_seed(self, e, t):
        r = e[:]
        self.out_tran(r, t)
        for n in range(16):
            r[n] += e[n]
        return r
    
    def out_tran(self, e, t):
        r = 0
        while r < t:
            self.do_tran(e, 0, 4, 8, 12)
            self.do_tran(e, 1, 5, 9, 13)
            self.do_tran(e, 2, 6, 10, 14)
            self.do_tran(e, 3, 7, 11, 15)
            r += 1
            if r >= t:
                break
            self.do_tran(e, 0, 5, 10, 15)
            self.do_tran(e, 1, 6, 11, 12)
            self.do_tran(e, 2, 7, 12, 13)
            self.do_tran(e, 3, 4, 13, 14)
            r += 1
        # print(e)
    
    def do_tran(self, e, t, r, n, a):
        e[t] += e[r]
        e[a] = self.do_calc(do_xor(e[a], e[t]), 16)
        e[n] += e[a]
        e[r] = self.do_calc(do_xor(e[r], e[n]), 12)
        e[t] += e[r]
        e[a] = self.do_calc(do_xor(e[a], e[t]), 8)
        e[n] += e[a]
        e[r] = self.do_calc(do_xor(e[r], e[n]), 7)
    
    def do_calc(self, e, t):
        return signed_left_shift(e, t) | unsigned_right_shift(e, 32 - t)
    
    def custom_random_result(self):
        rounds = 0
        rand_key = []
        keyString = []
        for i in range(12):
            rand_key.append(math.floor(self.custom_rand() * math.pow(2, 32)))
            rounds = ((rand_key[i] & 15) + rounds) & 15
            keyString.append(rand_key[i] & 255)
            keyString.append(unsigned_right_shift(rand_key[i], 8) & 255)
            keyString.append(unsigned_right_shift(rand_key[i], 16) & 255)
            keyString.append(unsigned_right_shift(rand_key[i], 24) & 255)
        return {
            "keyString": keyString, 
            "rand_key": rand_key, 
            "rounds": rounds+5
        }

class CreateStrWithRand(CustomRandom):
    def __init__(self, origin_str: str, timestamp: int=0, test_hd: list=[], gd: int=1, first_str: str="L", inner_encode_type: str="s3"):
        super().__init__(timestamp=timestamp, test_hd=test_hd, gd=gd)
        self.first_str = first_str
        self.inner_encode_type = inner_encode_type
        self.id = [1196819126,600974999,3863347763,1451689750]
        self.origin_str = origin_str

    def init_new_str(self, e, t, r):
        n = len(r) // 4
        a = len(r) % 4
        o = (len(r) + 3) // 4
        i = [0] * o
        for s in range(n):
            u = 4 * s
            i[s] = r[u] | signed_left_shift(r[u + 1], 8) | signed_left_shift(r[u + 2], 16) | signed_left_shift(r[u + 3], 24)
        s = n
        if a > 0:
            i[s] = 0
            for l in range(a):
                i[s] |= r[4 * s + l] << (8 * l)
        self.inner_init(e, t, i)
        for s in range(n):
            g = 4 * s
            r[g] = i[s] & 0xFF
            r[g + 1] = unsigned_right_shift(i[s], 8) & 0xFF
            r[g + 2] = unsigned_right_shift(i[s], 16) & 0xFF
            r[g + 3] = unsigned_right_shift(i[s], 24) & 0xFF
        if a > 0:
            s += 1
            for f in range(a):
                r[4 * s + f] = unsigned_right_shift(i[s], 8 * f) & 0xFF

    def inner_init(self, e, t, r):
        n = e[:]
        a = 0
        while a + 16 < len(r):
            o = self.get_seed(n, t)
            n[12] = n[12] + 1 & 4294967295
            for i in range(16):
                r[a + i] ^= o[i]
            a += 16
        s = len(r) - a
        u = self.get_seed(n, t)
        for l in range(s):
            r[a + l] ^= u[l]
    
    def create_new_str(self, e, t, r):
        self.id.extend(e)
        n = [ord(ch) for ch in r]
        self.init_new_str(self.id, t, n)
        return "".join(map(chr, n))
    
    def get_substr_num(self, substr_num, index_obj, new_enc_str_len):
        if not substr_num:
            substr_num = 0
        for i in range(len(index_obj)):
            if isinstance(index_obj, list):
                byte_ele = index_obj[i]
            elif isinstance(index_obj, str):
                byte_ele = ord(index_obj[i])
            substr_num = (substr_num + byte_ele) % (new_enc_str_len + 1)
        return substr_num
    
    def main(self):
        rand_result = self.custom_random_result()
        keyString = rand_result.get("keyString")
        rand_key = rand_result.get("rand_key")
        rounds = rand_result.get("rounds")
        if (not keyString) or (not rand_key) or (not rounds):
            return ""
        new_enc_str = self.create_new_str(rand_key, rounds, self.origin_str)
        mid_substr_num = self.get_substr_num(0, keyString, len(new_enc_str))
        substr_num = self.get_substr_num(mid_substr_num, new_enc_str, len(new_enc_str))
        before_str = self.first_str + new_enc_str[:substr_num] + "".join(map(chr, keyString)) + new_enc_str[substr_num:]
        result = inner_encode(before_str, self.inner_encode_type)
        return result

def get_page_sign(at_time: int=0) -> int:
    page_time = at_time if at_time else int(time.time() * 1000)
    return math.floor(abs(((page_time + random.random() + random.random()) * 1000) % 2147483648))

def get_encode_array(origin_array: list) -> list:
    origin_array_len = len(origin_array)
    encode_array = [origin_array_len]
    for i in range(origin_array_len):
        if isinstance(origin_array[i], int):
            value_array = list(origin_array[i].to_bytes(2, byteorder='big')) if origin_array[i] <= 65535 else list(origin_array[i].to_bytes(4, byteorder='big'))
        elif isinstance(origin_array[i], str):
            value_array = list(origin_array[i].encode('utf-8'))
        
        key_array = [0, len(value_array)]
        encode_array.append(i)
        encode_array.extend(key_array)
        encode_array.extend(value_array)
    return encode_array

# X-Gnarly
# params 不带生成的 X-Bogus，timestamp 13位
def get_XGnarly(params: dict={}, data: Union[dict, str]={}, no_json: bool=False, ua: str="", timestamp: int=0, canvas_num: int=2010578131, test_hd: list=[], extra_num: int=0, page_sign: int=0):
    # data 不是 json 而是正常表单数据的情况，入参 no_json: True
    if isinstance(data, dict):
        if no_json:
            data = tk_urlencode_2(data)
        else:
            data = json.dumps(data, separators=(",", ":"), ensure_ascii=False)
    else:
        data=data

    if not page_sign:
        page_sign = get_page_sign(at_time=timestamp)

    origin_array = [
        0, 
        1, 
        extra_num, 
        do_md5hash(tk_urlencode_2(params).encode("utf-8")), 
        do_md5hash(data.encode("utf-8")), 
        do_md5hash(ua.encode("utf-8")), 
        int(timestamp / 1000), 
        canvas_num, 
        page_sign, 
        "5.1.0"
    ]
    reduce_array = [int(ele) if str(ele).isdigit() else 0 for ele in origin_array]
    first_identity = reduce(lambda a, b: do_xor(a, b), reduce_array)
    origin_array[0] = unsigned_right_shift(first_identity, 0 % 4294967295)
    encode_array = get_encode_array(origin_array)
    origin_str = "".join(map(chr, encode_array))
    XGnarly = CreateStrWithRand(origin_str=origin_str, timestamp=timestamp, test_hd=test_hd, gd=1, first_str="K", inner_encode_type="s3").main()
    return XGnarly

# X-Bogus、X-Gnarly 上层封装，返回 params、data
# now_time：13位时间戳
def group_XBogus_XGnarly(params: dict={}, data: Union[dict, str]={}, no_json: bool=False, ua: str="", now_time: int=0, canvas_num: int=2010578131, extra_num: int=0, page_sign: int=0):
    X_Bogus = get_XBogus(params=params,  data=data, ua=ua,  now_time=int(now_time / 1000), canvas_num=canvas_num, extra_num=extra_num, no_json=no_json)
    X_Gnarly = get_XGnarly(params=params, data=data, no_json=no_json,  ua=ua, timestamp=now_time, canvas_num=canvas_num,  extra_num=extra_num, page_sign=page_sign)
    params["X-Bogus"] = X_Bogus
    params["X-Gnarly"] = X_Gnarly
    return params, data

if __name__ == "__main__":
    # 获取 canvas_num
    canvas_str = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAQCAYAAABQrvyxAAAAAXNSR0IArs4c6QAAAkxJREFUSEvFlr9rVEEQxz+xFP0DFFHS2tooJCqpFKIgWKiNksJCUggptBEsItFCsFALIYUWYiEIJiBamJxY2KRIKQghQdTSwsJK3W/YgXEy++6KnG7z7u2+nf3+mJm9Ef4ev8O7XkfqnNbsd/LZP586BNzIABnQCDiSGyaZPcAHYH+QpQdMAj+ALQQy9W1/BPs/3DgFnAUuVlCpA5nqPoW8IMN0IObjLmAReARcBo7VD3qZsnFzqwY82WvAFWAaeACcAVYAzd8GnjjlFF/qvQN2GhCXGlkxKc7BJMaWGuhyQIGzOpC1Lx2Q98BYeb8JXAXuVQJGyvJbZBcAU/cVcCdBL/AnK8HdoTZSBwZRPKovdZ7Xw5WnBwr478B8sfwL8MK54gmbMx6kClTDiC4H5Y1jZxGLhKntf1tb9Wt2kNak6jNgBjgHnC95uzcQsBTypCKBx8Bx4DDwtdGgUwIG1j8tdbK1WeAnoKeG1L0PzAFHqnI6yIP13xnAzAGvtK8Xm7+ulPNF3Gqj0QEvyNMC9HXNcQ9sDbhbc9wT+Fi7ibrIhlO4RcAEiU5Y3az3a4Vd/V5r34Bx4JNzQAXtwXkC+uwS8KY61c8BpZJqS8Ueh+LOtxwYtAak/gkX2QrUt82WA5spUPd2OaCaOlrbsh2VOhD/Qnj1szWpf6FcKkuNItuu6XhnWNzTciY6EAvVdx1b2wd8rlH6peB2kWjGyQDsAH41dkwBt+pta51n6CC7DsgI6MZ8WPLubWmD6iYao+XqnyiX0WrN22GnzcCi/AE0X6vD5MzcIwAAAABJRU5ErkJggg=="
    canvas_num = get_canvas_num(canvas_str) # 2010578131
    print(canvas_num)

    # 改头像相关参数 ——————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————
    msToken = "b0X5ti1mHhFtSGU6XWBujmclpjPbRjXBoqqhZSyEoX44MsMzABt-pvXhRqTIR73BauPlony787u0sb6rvvxdwt43Y6klTWhTZU1-K6GAvNfMAiVx_5FxYN6d5P7B-Uf9AjDQi7V-ZwvvlbQ5m7ba6q2Cyg=="
    params = {
        "WebIdLastTime": "1739155991",
        "aid": "1988",
        "app_language": "en",
        "app_name": "tiktok_web",
        "browser_language": "zh-CN",
        "browser_name": "Mozilla",
        "browser_online": "true",
        "browser_platform": "Win32",
        "browser_version": "5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36",
        "channel": "tiktok_web",
        "cookie_enabled": "true",
        "data_collection_enabled": "true",
        "device_id": "7469617955463267857",
        "device_platform": "web_pc",
        "focus_state": "true",
        "from_page": "user",
        "history_len": "2",
        "is_fullscreen": "false",
        "is_page_visible": "true",
        "odinId": "7479439474575066142",
        "os": "windows",
        "priority_region": "",
        "referer": "https://www.tiktok.com/@a1b2c3xyw",
        "region": "US",
        "root_referer": "https://www.tiktok.com/@a1b2c3xyw",
        "screen_height": "1080",
        "screen_width": "1920",
        "tz_name": "Asia/Shanghai",
        "user_is_login": "true",
        "webcast_language": "en",
        "msToken": msToken
    }
    data = {
        "signature": "嘿嘿嘿hello",
        "tt_csrf_token": "ZTlAfTfs-tyFOc04Oe9-KeHa7VoyoWptYkoU"
    }
    ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"
    now_time = int(1745717497740 / 1000)
    canvas_num = 2010578131
    X_Bogus = get_XBogus(params, "", ua, now_time, canvas_num, extra_num=0, no_json=True)
    print(X_Bogus) # DFSzswVL9FeDvRSxCab8bOTQh4eB

    X_Gnarly = get_XGnarly(
        params=params,
        data=data,
        no_json=True, 
        ua=ua,
        timestamp=int(1745720334565), # 13 位
        canvas_num=canvas_num, 
        test_hd=[2517678443,2718276124,3212677781,2633865432,217618912,2931180889,1498001188,2157053261,211147047,185100057,2903579748,3732962506,1960555793,1300483087,4223367988,3803680937],
        extra_num=0,
        page_sign=2050677371
    )
    print(X_Gnarly) # MFdAdxCBpfU6TOvUlm5fPVPVg3g4RG667XfWJfj47plvILf1WGVkjibQeHQoXZezeiRP7VFZffR3ivbmuq9p10aD50OXX9Ivr1uh/mSNgRWD3mdCPKjjiD2ergOBPU58oWCiO8cZHvg1aQCkDhpH2h42kvRWu4DrBX2dEnMUrWrIl1qs0VQjsrwqvlPUoRI/NinbTVlEgAUU-6znVvxhFiK6gRcI2DX2Rs94POcqUq6JX1BpwGWF0Nxwpns91xIfBPbazxVCO3cl

    # 发布视频相关参数 ——————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————
    msToken = "0MnGEI68BrNJ3WzKThpev5O8QWIKDP1wMaBguqs67MR8mLAqh2GHs_CP41uBCh4HK_n0K371hjK_enkTnxtFJ74IWVm5KvosTPjKgjnKi26k9Pv2VF2oQCS_bMVl_n9FxHEkcfrnx6Qjqj3UNeyXs3nmQw=="
    url = "https://www.tiktok.com/tiktok/web/project/post/v1/"
    params = {
        "app_name": "tiktok_web",
        "channel": "tiktok_web",
        "device_platform": "web",
        "tz_name": "Asia/Shanghai",
        "aid": "1988",
        "msToken": msToken
    }
    data = {"post_common_info":{"creation_id":"0Mqc5weshPiEOtYZLB4XX","enter_post_page_from":1,"post_type":3},"feature_common_info_list":[{"geofencing_regions":[],"playlist_name":"","playlist_id":"","tcm_params":"{\"commerce_toggle_info\":{}}","sound_exemption":0,"anchors":[],"vedit_common_info":{"draft":"","video_id":"v15025gf0000cv3rv0nog65ji0l9vatg"},"privacy_setting_info":{"visibility_type":0,"allow_duet":1,"allow_stitch":1,"allow_comment":1},"content_check_id":""}],"single_post_req_list":[{"batch_index":0,"video_id":"v15025gf0000cv3rv0nog65ji0l9vatg","is_long_video":0,"single_post_feature_info":{"text":"beautiful","text_extra":[],"markup_text":"beautiful","music_info":{},"poster_delay":0,"cloud_edit_video_height":1024,"cloud_edit_video_width":576,"cloud_edit_is_use_video_canvas":False}}]}
    ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36"
    # now_time = int(time.time()) # 要求 10 位
    now_time = int(1741144054725 / 1000)
    canvas_num = 2010578131
    X_Bogus = get_XBogus(params, data, ua, now_time, canvas_num, extra_num=128)
    print(X_Bogus) # DFSzswcLdvKX0oZatZ0ao5TQh4S/

    # 获取 _signature ：使用和前面生成 X_Bogus 一致的 参数，同时验证 X_Bogus 和 _signature
    now_time = int(1741144268259 / 1000)
    location_href = "https://www.tiktok.com/tiktokstudio/upload?from=webapp"
    _signature = get_signature(url=url, params=params, X_Bogus=X_Bogus, data=data, ua=ua, now_time=now_time, location_href=location_href, canvas_num=canvas_num)
    print(_signature) # _02B4Z6wo00001XYRVGwAAIDAqUqnI.LEcKF2EVDAADpH43

    # ————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————————
    # 获取 _signature ：已知 X_Bogus 验证 _signature
    import time
    msToken = "g_tiaymR7ILb_dw8VRcd5YMdbVJYQBCmrXoL9jJEcJkz1RfAXU-lrU9GsFLKS40I_y90HzoXf6yACUCE4P5_APoA56806RdM1LCVMhwwgQ84l18GB9FUzyqTpzd87NfRPHkc7d7iuT59s2M"
    # 发布视频相关参数
    url = "https://www.tiktok.com/tiktok/web/project/post/v1/"
    params = {
        "app_name": "tiktok_web",
        "channel": "tiktok_web",
        "device_platform": "web",
        "tz_name": "Asia/Shanghai",
        "aid": "1988",
        "msToken": msToken
    }
    data = {"post_common_info":{"creation_id":"b_BVxwhYJ2gCCFjllQ-3q","enter_post_page_from":1,"post_type":3},"feature_common_info_list":[{"geofencing_regions":[],"playlist_name":"","playlist_id":"","tcm_params":"{\"commerce_toggle_info\":{}}","sound_exemption":0,"anchors":[],"vedit_common_info":{"draft":"","video_id":"v15025gf0000curf0cvog65ta5kjbgq0"},"privacy_setting_info":{"visibility_type":0,"allow_duet":1,"allow_stitch":1,"allow_comment":1},"content_check_id":""}],"single_post_req_list":[{"batch_index":0,"video_id":"v15025gf0000curf0cvog65ta5kjbgq0","is_long_video":0,"single_post_feature_info":{"text":"beautiful","text_extra":[],"markup_text":"beautiful","music_info":{},"poster_delay":0,"cloud_edit_video_height":1024,"cloud_edit_video_width":576,"cloud_edit_is_use_video_canvas":False}}]}
    ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36"
    # now_time = int(time.time()) # 要求 10 位
    canvas_num = 2010578131
    X_Bogus = "DFSzswcLIARWhoZatDKykcTQh4SN"
    now_time = int(1740044521850 / 1000)
    location_href = "https://www.tiktok.com/tiktokstudio/upload?from=webapp"
    _signature = get_signature(url=url, params=params, X_Bogus=X_Bogus, data=data, ua=ua, now_time=now_time, location_href=location_href, canvas_num=canvas_num)
    print(_signature) # _02B4Z6wo000017McrMwAAIDCbEdfgPtWpy-zHKhAAIt69c