# 主控
import json
import logging
import logging.handlers
import logging.handlers
import multiprocessing
import os
import sys
import threading
import time
from pathlib import Path
from typing import TYPE_CHECKING, Union, List, Tuple, Callable, Optional
import warnings

from manager_base.baseManager import BaseManager
from manager_base.midManager import run_mid_manager
from utils.common import PAGINATED_COLLECT_TASKS
from utils.msTokenUtil import get_tiktok_token

if TYPE_CHECKING:
    from requests import Response


class Manager(BaseManager):
    def __init__(self):
        # self.test_botton = 1 # 测试数据

        self.task_queues_dict = None  # 每种任务类型自己的队列，下载任务队列
        self.heartbeat_queue = None
        self.upload_batch_queue = None
        self.upload_queue = None
        self.log_queue = None
        self.spider_logger_dict = {}
        self.spider_log_queue_dict = {}
        self.log_listener_list: list[logging.handlers.QueueListener] = []
        self.heartbeat_dict = {}
        self.download_threads: List[Tuple[threading.Thread, str, Callable]] = []
        self.processes: dict[int, multiprocessing.Process] = {}
        self.pid_to_tasktype = {}
        self._last_heartbeat_log = 0  # 上次记录心跳的时间

        config_path = os.path.join(main_path, 'config.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            self.init_info = json.loads(f.read())
        super().__init__(main_path=main_path, init_info=self.init_info)
        self.restart_time = self.init_info.get("restart_time", 3600)
        self.shared_namespace = None

    def _update_token_logic(self):
        """
        更新 token 的核心逻辑。
        """
        self.logger.info("开始执行更新 ms_token 的定时任务...")
        try:
            current_token = self.shared_namespace.default_ms_token if self.shared_namespace else self.init_info[
                "default_ms_token"]
            proxy = self.proxies.get("https") if self.proxies else None

            token_info = get_tiktok_token(config=self.init_info, token=current_token, proxy=proxy)

            if token_info and token_info.get("msToken"):
                new_token = token_info["msToken"]
                self.logger.info(f"成功获取到新的 ms_token: {new_token}")
                if self.shared_namespace:
                    self.shared_namespace.default_ms_token = new_token
                    self.logger.info(f"共享 ms_token 已更新。")
            else:
                self.logger.warning("未能从API获取到新的msToken。")
        except Exception as e:
            self.logger.error(f"[msToken] 更新异常 | 详情: {str(e)}", exc_info=True)

    def _token_updater_thread(self):
        """
        一个守护线程，每隔24小时触发一次 token 更新任务。
        """
        while True:
            self._update_token_logic()
            time.sleep(24 * 60 * 60)  # 等待24小时

    def init_logger(self, name: str, log_level="DEBUG", queue: Optional[multiprocessing.Queue] = None,
                    with_stream=True):
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, log_level.upper(), logging.DEBUG))
        logger.propagate = False
        # 简化的日志格式，异步写入时，包含进程名
        formatter = logging.Formatter('%(asctime)s - %(processName)s - %(levelname)s - %(message)s')
        if with_stream:
            stream_handler = logging.StreamHandler()
            stream_handler.setLevel(logging.NOTSET)
            stream_handler.setFormatter(formatter)
            logger.addHandler(stream_handler)
        if queue:
            queue_handler = logging.handlers.QueueHandler(queue)
            logger.addHandler(queue_handler)
        return logger

    def start_log_listener(self, log_queue, log_name, spider_log=False):
        log_base_path = os.path.join(self.main_path, 'logs', log_name) if spider_log else os.path.join(self.main_path, 'logs')
        os.makedirs(log_base_path, exist_ok=True)
        handler = logging.handlers.TimedRotatingFileHandler(
            os.path.join(log_base_path, f'{log_name}.log'), when='D', interval=1, backupCount=15, encoding='utf-8'
        )
        formatter = logging.Formatter('%(asctime)s - %(filename)s:%(lineno)d - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        listener = logging.handlers.QueueListener(log_queue, handler)
        listener.start()
        return listener

    def __download_task(self, task_type: str):
        while True:
            try:
                response: "Response" = self.do_req(method="GET", url=self.join_url_params(self.task_url, params={"type": task_type, "platform": self.init_info["platform"]}), headers=self.headers, no_proxy=True)
                if response.text:
                    response_json: dict = response.json()
                    """
                    if task_type == "comment_collect" and self.test_botton:
                        response_json = {'data': {'details': [{'cookie_dict': {'_fbp': 'fb.1.1741571212518.1648668569', '_ga': 'GA1.1.1992765033.1740710270', '_ga_LWWPCY99PB': 'GS1.1.1746768732.20.1.1746768948.0.0.1317304137', 'cmpl_token': 'AgQQAPO8F-RO0rTmK7qGvN08_cjwx8Tbv5M3YN6jJQ', 'cookie-consent': '{%22optional%22:true%2C%22ga%22:true%2C%22af%22:true%2C%22fbp%22:true%2C%22lip%22:true%2C%22bing%22:true%2C%22ttads%22:true%2C%22reddit%22:true%2C%22hubspot%22:true%2C%22version%22:%22v10%22}', 'd_ticket': '1d1d03ebcded9d736f486165e65cf9fe77a59', 'delay_guest_mode_vid': '5', 'last_login_method': 'handle', 'living_user_id': '************', 'msToken': 'IFF8HufmT5FYLYlJgT_fRXhZ-vv1ZVj69og46STQY74mJp3zRSBXeHP4sycaE3FfVIfz6ifXOJot5L_8xoO4r10QL0bA_kNUWXuo7yu9p1aVht6JYNF6zMxDUoBMNYYzkdXjZfYg9pOtYB4=', 'multi_sids': '7479439474575066142%3A3ecf9e095e2871147a39adbc5bbef02b%7C7266774684409725958%**********************************%7C7266774587765376006%3Ae4b13e4cb171b58c301316890a508bff%7C7266774727351206917%3A992489de3c7a0ccac47c0a62640a1393', 'odin_tt': '5f622c5619101388e36e439aa17ff573e3eb6b70d3eb7c8cb0c926543ef865ec85e8ccfab1b284b83ba7fb848de836911a44fd40246e0502d8d71dbd918b2f777111f2bbd5286f630b8df6826bbc9212', 'passport_csrf_token': 'ce0f1134c49044fa503b49df1c463dbf', 'passport_csrf_token_default': 'ce0f1134c49044fa503b49df1c463dbf', 'passport_fe_beating_status': 'true', 'perf_feed_cache': '{%22expireTimestamp%22:1750845600000%2C%22itemIds%22:[%227507828074591816990%22%2C%227506102228789349654%22%2C%227486751505681255688%22]}', 's_v_web_id': 'verify_mc9vbrqd_PeEtYeqZ_8uCu_4AgO_BgtN_3UtyEF2tDtKy', 'sessionid': '992489de3c7a0ccac47c0a62640a1393', 'sessionid_ss': '992489de3c7a0ccac47c0a62640a1393', 'sid_guard': '992489de3c7a0ccac47c0a62640a1393%7C1750730008%7C15551982%7CSun%2C+21-Dec-2025+01%3A53%3A10+GMT', 'sid_tt': '992489de3c7a0ccac47c0a62640a1393', 'sid_ucp_v1': '1.0.0-KDFlYTFhM2VhZTUxYjU0NzUwYTZkNTI2NzdkMGM3MDI5M2M3NzBjMWEKGgiFiLj00KOx7GQQmIrowgYYsws4BEDqB0gEEAMaAm15IiA5OTI0ODlkZTNjN2EwY2NhYzQ3YzBhNjI2NDBhMTM5Mw', 'ssid_ucp_v1': '1.0.0-KDFlYTFhM2VhZTUxYjU0NzUwYTZkNTI2NzdkMGM3MDI5M2M3NzBjMWEKGgiFiLj00KOx7GQQmIrowgYYsws4BEDqB0gEEAMaAm15IiA5OTI0ODlkZTNjN2EwY2NhYzQ3YzBhNjI2NDBhMTM5Mw', 'store-country-code': 'sg', 'store-country-code-src': 'uid', 'store-country-sign': 'MEIEDMX4JVXBaSJLoYn9RwQga3xgMhS5obBFCnqMqkBYJFMXgK_GmcQ8_y9_J9twoZMEECCklb1TDqbY3CwAsCb6AEI', 'store-idc': 'alisg', 'tiktok_webapp_lang': 'en', 'tiktok_webapp_theme': 'light', 'tiktok_webapp_theme_source': 'auto', 'tt-target-idc': 'alisg', 'tt-target-idc-sign': 'hNKMuv5OxOTpoGZq8XmlquO08veYaNHmhE9Lr3TNVPBxkXE3vW-RU066F2oreF5CYhpCq0VJknokb6D3sBnIIsP0zD2TmysGzgHAaO8FMn8fQxVWErzsmFlE-_qnakmOV3AYTnRMhob9rH6Ho9tSLzJN3bsIgApnfDn6O8cuGrsIgu_5I33YaMZqxEFZl4FMB_C3XexKEfYwKi4OPHndfMCAKJk4VtYcOyumGHcZvaAyQpo2jiOtUzyARxTAiwmEqlXivb3Jag8v60Em-N3-RJ6coKOT-_GR9jotpEzDa5kuiQv87Jjk9bm8ikshm5Yn7W6IX2KmggTMQoDBOOrUW0_Cm0LLFJ-I10Erov_qpni6bItBWRLhf0cgyRElx0Db9DSVYXiOV1_ZNQvoUO07Vft3qBOr5ba1ka_lhWYAkdIScs6wQZy32RaeTYBPS-GFWM8K9xTWjR_bAMY5W7IJqKEnQrHBrrhprZ81ssMqxWSs0P9bzZcA6TMV7GN5wPcg', 'tt_chain_token': '2GZd9VD8sWSyFc8kdCXtFA==', 'tt_csrf_token': 'KRVCAJ4R-BWT2PsGtOpwn3VkjR3HZ9fxmimw', 'ttwid': '1%7CgSu5bFeW1wYzN91HlTRp9Omr2FZQN3lhOjZpYMwJd7M%7C1750730008%7C7068651bf0e2393d8f526a24a1791e1de93fac15d5b38f3038b941f0437e454f', 'uid_tt': '18115fbfaaa9aa14c2c66c9604fdc685b7c09af5b4348cbef78461adcac563b8', 'uid_tt_ss': '18115fbfaaa9aa14c2c66c9604fdc685b7c09af5b4348cbef78461adcac563b8'}, 'message_text': '向往', 'object': 'oxmoxmoxmoxm', 'receiver_username': 'neshiajc', 'small_task_id': 5453, 'task_type': 'private_message', 'user_app': False}, {'cookie_dict': {'_fbp': 'fb.1.1741571212518.1648668569', '_ga': 'GA1.1.1992765033.1740710270', '_ga_LWWPCY99PB': 'GS1.1.1746768732.20.1.1746768948.0.0.1317304137', 'cmpl_token': 'AgQQAPO8F-RO0rTmK7qGvN08_cjwx8Tbv5M3YN6jJQ', 'cookie-consent': '{%22optional%22:true%2C%22ga%22:true%2C%22af%22:true%2C%22fbp%22:true%2C%22lip%22:true%2C%22bing%22:true%2C%22ttads%22:true%2C%22reddit%22:true%2C%22hubspot%22:true%2C%22version%22:%22v10%22}', 'd_ticket': '1d1d03ebcded9d736f486165e65cf9fe77a59', 'delay_guest_mode_vid': '5', 'last_login_method': 'handle', 'living_user_id': '************', 'msToken': 'IFF8HufmT5FYLYlJgT_fRXhZ-vv1ZVj69og46STQY74mJp3zRSBXeHP4sycaE3FfVIfz6ifXOJot5L_8xoO4r10QL0bA_kNUWXuo7yu9p1aVht6JYNF6zMxDUoBMNYYzkdXjZfYg9pOtYB4=', 'multi_sids': '7479439474575066142%3A3ecf9e095e2871147a39adbc5bbef02b%7C7266774684409725958%**********************************%7C7266774587765376006%3Ae4b13e4cb171b58c301316890a508bff%7C7266774727351206917%3A992489de3c7a0ccac47c0a62640a1393', 'odin_tt': '5f622c5619101388e36e439aa17ff573e3eb6b70d3eb7c8cb0c926543ef865ec85e8ccfab1b284b83ba7fb848de836911a44fd40246e0502d8d71dbd918b2f777111f2bbd5286f630b8df6826bbc9212', 'passport_csrf_token': 'ce0f1134c49044fa503b49df1c463dbf', 'passport_csrf_token_default': 'ce0f1134c49044fa503b49df1c463dbf', 'passport_fe_beating_status': 'true', 'perf_feed_cache': '{%22expireTimestamp%22:1750845600000%2C%22itemIds%22:[%227507828074591816990%22%2C%227506102228789349654%22%2C%227486751505681255688%22]}', 's_v_web_id': 'verify_mc9vbrqd_PeEtYeqZ_8uCu_4AgO_BgtN_3UtyEF2tDtKy', 'sessionid': '992489de3c7a0ccac47c0a62640a1393', 'sessionid_ss': '992489de3c7a0ccac47c0a62640a1393', 'sid_guard': '992489de3c7a0ccac47c0a62640a1393%7C1750730008%7C15551982%7CSun%2C+21-Dec-2025+01%3A53%3A10+GMT', 'sid_tt': '992489de3c7a0ccac47c0a62640a1393', 'sid_ucp_v1': '1.0.0-KDFlYTFhM2VhZTUxYjU0NzUwYTZkNTI2NzdkMGM3MDI5M2M3NzBjMWEKGgiFiLj00KOx7GQQmIrowgYYsws4BEDqB0gEEAMaAm15IiA5OTI0ODlkZTNjN2EwY2NhYzQ3YzBhNjI2NDBhMTM5Mw', 'ssid_ucp_v1': '1.0.0-KDFlYTFhM2VhZTUxYjU0NzUwYTZkNTI2NzdkMGM3MDI5M2M3NzBjMWEKGgiFiLj00KOx7GQQmIrowgYYsws4BEDqB0gEEAMaAm15IiA5OTI0ODlkZTNjN2EwY2NhYzQ3YzBhNjI2NDBhMTM5Mw', 'store-country-code': 'sg', 'store-country-code-src': 'uid', 'store-country-sign': 'MEIEDMX4JVXBaSJLoYn9RwQga3xgMhS5obBFCnqMqkBYJFMXgK_GmcQ8_y9_J9twoZMEECCklb1TDqbY3CwAsCb6AEI', 'store-idc': 'alisg', 'tiktok_webapp_lang': 'en', 'tiktok_webapp_theme': 'light', 'tiktok_webapp_theme_source': 'auto', 'tt-target-idc': 'alisg', 'tt-target-idc-sign': 'hNKMuv5OxOTpoGZq8XmlquO08veYaNHmhE9Lr3TNVPBxkXE3vW-RU066F2oreF5CYhpCq0VJknokb6D3sBnIIsP0zD2TmysGzgHAaO8FMn8fQxVWErzsmFlE-_qnakmOV3AYTnRMhob9rH6Ho9tSLzJN3bsIgApnfDn6O8cuGrsIgu_5I33YaMZqxEFZl4FMB_C3XexKEfYwKi4OPHndfMCAKJk4VtYcOyumGHcZvaAyQpo2jiOtUzyARxTAiwmEqlXivb3Jag8v60Em-N3-RJ6coKOT-_GR9jotpEzDa5kuiQv87Jjk9bm8ikshm5Yn7W6IX2KmggTMQoDBOOrUW0_Cm0LLFJ-I10Erov_qpni6bItBWRLhf0cgyRElx0Db9DSVYXiOV1_ZNQvoUO07Vft3qBOr5ba1ka_lhWYAkdIScs6wQZy32RaeTYBPS-GFWM8K9xTWjR_bAMY5W7IJqKEnQrHBrrhprZ81ssMqxWSs0P9bzZcA6TMV7GN5wPcg', 'tt_chain_token': '2GZd9VD8sWSyFc8kdCXtFA==', 'tt_csrf_token': 'KRVCAJ4R-BWT2PsGtOpwn3VkjR3HZ9fxmimw', 'ttwid': '1%7CgSu5bFeW1wYzN91HlTRp9Omr2FZQN3lhOjZpYMwJd7M%7C1750730008%7C7068651bf0e2393d8f526a24a1791e1de93fac15d5b38f3038b941f0437e454f', 'uid_tt': '18115fbfaaa9aa14c2c66c9604fdc685b7c09af5b4348cbef78461adcac563b8', 'uid_tt_ss': '18115fbfaaa9aa14c2c66c9604fdc685b7c09af5b4348cbef78461adcac563b8'}, 'message_text': '向往', 'object': 'oxmoxmoxmoxm', 'receiver_username': 'seo_on.tv', 'small_task_id': 5454, 'task_type': 'private_message', 'user_app': False}, {'cookie_dict': {'_fbp': 'fb.1.1741571212518.1648668569', '_ga': 'GA1.1.1992765033.1740710270', '_ga_LWWPCY99PB': 'GS1.1.1746768732.20.1.1746768948.0.0.1317304137', 'cmpl_token': 'AgQQAPO8F-RO0rTmK7qGvN08_cjwx8Tbv5M3YN6jJQ', 'cookie-consent': '{%22optional%22:true%2C%22ga%22:true%2C%22af%22:true%2C%22fbp%22:true%2C%22lip%22:true%2C%22bing%22:true%2C%22ttads%22:true%2C%22reddit%22:true%2C%22hubspot%22:true%2C%22version%22:%22v10%22}', 'd_ticket': '1d1d03ebcded9d736f486165e65cf9fe77a59', 'delay_guest_mode_vid': '5', 'last_login_method': 'handle', 'living_user_id': '************', 'msToken': 'IFF8HufmT5FYLYlJgT_fRXhZ-vv1ZVj69og46STQY74mJp3zRSBXeHP4sycaE3FfVIfz6ifXOJot5L_8xoO4r10QL0bA_kNUWXuo7yu9p1aVht6JYNF6zMxDUoBMNYYzkdXjZfYg9pOtYB4=', 'multi_sids': '7479439474575066142%3A3ecf9e095e2871147a39adbc5bbef02b%7C7266774684409725958%**********************************%7C7266774587765376006%3Ae4b13e4cb171b58c301316890a508bff%7C7266774727351206917%3A992489de3c7a0ccac47c0a62640a1393', 'odin_tt': '5f622c5619101388e36e439aa17ff573e3eb6b70d3eb7c8cb0c926543ef865ec85e8ccfab1b284b83ba7fb848de836911a44fd40246e0502d8d71dbd918b2f777111f2bbd5286f630b8df6826bbc9212', 'passport_csrf_token': 'ce0f1134c49044fa503b49df1c463dbf', 'passport_csrf_token_default': 'ce0f1134c49044fa503b49df1c463dbf', 'passport_fe_beating_status': 'true', 'perf_feed_cache': '{%22expireTimestamp%22:1750845600000%2C%22itemIds%22:[%227507828074591816990%22%2C%227506102228789349654%22%2C%227486751505681255688%22]}', 's_v_web_id': 'verify_mc9vbrqd_PeEtYeqZ_8uCu_4AgO_BgtN_3UtyEF2tDtKy', 'sessionid': '992489de3c7a0ccac47c0a62640a1393', 'sessionid_ss': '992489de3c7a0ccac47c0a62640a1393', 'sid_guard': '992489de3c7a0ccac47c0a62640a1393%7C1750730008%7C15551982%7CSun%2C+21-Dec-2025+01%3A53%3A10+GMT', 'sid_tt': '992489de3c7a0ccac47c0a62640a1393', 'sid_ucp_v1': '1.0.0-KDFlYTFhM2VhZTUxYjU0NzUwYTZkNTI2NzdkMGM3MDI5M2M3NzBjMWEKGgiFiLj00KOx7GQQmIrowgYYsws4BEDqB0gEEAMaAm15IiA5OTI0ODlkZTNjN2EwY2NhYzQ3YzBhNjI2NDBhMTM5Mw', 'ssid_ucp_v1': '1.0.0-KDFlYTFhM2VhZTUxYjU0NzUwYTZkNTI2NzdkMGM3MDI5M2M3NzBjMWEKGgiFiLj00KOx7GQQmIrowgYYsws4BEDqB0gEEAMaAm15IiA5OTI0ODlkZTNjN2EwY2NhYzQ3YzBhNjI2NDBhMTM5Mw', 'store-country-code': 'sg', 'store-country-code-src': 'uid', 'store-country-sign': 'MEIEDMX4JVXBaSJLoYn9RwQga3xgMhS5obBFCnqMqkBYJFMXgK_GmcQ8_y9_J9twoZMEECCklb1TDqbY3CwAsCb6AEI', 'store-idc': 'alisg', 'tiktok_webapp_lang': 'en', 'tiktok_webapp_theme': 'light', 'tiktok_webapp_theme_source': 'auto', 'tt-target-idc': 'alisg', 'tt-target-idc-sign': 'hNKMuv5OxOTpoGZq8XmlquO08veYaNHmhE9Lr3TNVPBxkXE3vW-RU066F2oreF5CYhpCq0VJknokb6D3sBnIIsP0zD2TmysGzgHAaO8FMn8fQxVWErzsmFlE-_qnakmOV3AYTnRMhob9rH6Ho9tSLzJN3bsIgApnfDn6O8cuGrsIgu_5I33YaMZqxEFZl4FMB_C3XexKEfYwKi4OPHndfMCAKJk4VtYcOyumGHcZvaAyQpo2jiOtUzyARxTAiwmEqlXivb3Jag8v60Em-N3-RJ6coKOT-_GR9jotpEzDa5kuiQv87Jjk9bm8ikshm5Yn7W6IX2KmggTMQoDBOOrUW0_Cm0LLFJ-I10Erov_qpni6bItBWRLhf0cgyRElx0Db9DSVYXiOV1_ZNQvoUO07Vft3qBOr5ba1ka_lhWYAkdIScs6wQZy32RaeTYBPS-GFWM8K9xTWjR_bAMY5W7IJqKEnQrHBrrhprZ81ssMqxWSs0P9bzZcA6TMV7GN5wPcg', 'tt_chain_token': '2GZd9VD8sWSyFc8kdCXtFA==', 'tt_csrf_token': 'KRVCAJ4R-BWT2PsGtOpwn3VkjR3HZ9fxmimw', 'ttwid': '1%7CgSu5bFeW1wYzN91HlTRp9Omr2FZQN3lhOjZpYMwJd7M%7C1750730008%7C7068651bf0e2393d8f526a24a1791e1de93fac15d5b38f3038b941f0437e454f', 'uid_tt': '18115fbfaaa9aa14c2c66c9604fdc685b7c09af5b4348cbef78461adcac563b8', 'uid_tt_ss': '18115fbfaaa9aa14c2c66c9604fdc685b7c09af5b4348cbef78461adcac563b8'}, 'message_text': '向往', 'object': 'oxmoxmoxmoxm', 'receiver_username': 'nastyblaq', 'small_task_id': 5455, 'task_type': 'private_message', 'user_app': False}, {'cookie_dict': {'_fbp': 'fb.1.1741571212518.1648668569', '_ga': 'GA1.1.1992765033.1740710270', '_ga_LWWPCY99PB': 'GS1.1.1746768732.20.1.1746768948.0.0.1317304137', 'cmpl_token': 'AgQQAPO8F-RO0rTmK7qGvN08_cjwx8Tbv5M3YN6jJQ', 'cookie-consent': '{%22optional%22:true%2C%22ga%22:true%2C%22af%22:true%2C%22fbp%22:true%2C%22lip%22:true%2C%22bing%22:true%2C%22ttads%22:true%2C%22reddit%22:true%2C%22hubspot%22:true%2C%22version%22:%22v10%22}', 'd_ticket': '1d1d03ebcded9d736f486165e65cf9fe77a59', 'delay_guest_mode_vid': '5', 'last_login_method': 'handle', 'living_user_id': '************', 'msToken': 'IFF8HufmT5FYLYlJgT_fRXhZ-vv1ZVj69og46STQY74mJp3zRSBXeHP4sycaE3FfVIfz6ifXOJot5L_8xoO4r10QL0bA_kNUWXuo7yu9p1aVht6JYNF6zMxDUoBMNYYzkdXjZfYg9pOtYB4=', 'multi_sids': '7479439474575066142%3A3ecf9e095e2871147a39adbc5bbef02b%7C7266774684409725958%**********************************%7C7266774587765376006%3Ae4b13e4cb171b58c301316890a508bff%7C7266774727351206917%3A992489de3c7a0ccac47c0a62640a1393', 'odin_tt': '5f622c5619101388e36e439aa17ff573e3eb6b70d3eb7c8cb0c926543ef865ec85e8ccfab1b284b83ba7fb848de836911a44fd40246e0502d8d71dbd918b2f777111f2bbd5286f630b8df6826bbc9212', 'passport_csrf_token': 'ce0f1134c49044fa503b49df1c463dbf', 'passport_csrf_token_default': 'ce0f1134c49044fa503b49df1c463dbf', 'passport_fe_beating_status': 'true', 'perf_feed_cache': '{%22expireTimestamp%22:1750845600000%2C%22itemIds%22:[%227507828074591816990%22%2C%227506102228789349654%22%2C%227486751505681255688%22]}', 's_v_web_id': 'verify_mc9vbrqd_PeEtYeqZ_8uCu_4AgO_BgtN_3UtyEF2tDtKy', 'sessionid': '992489de3c7a0ccac47c0a62640a1393', 'sessionid_ss': '992489de3c7a0ccac47c0a62640a1393', 'sid_guard': '992489de3c7a0ccac47c0a62640a1393%7C1750730008%7C15551982%7CSun%2C+21-Dec-2025+01%3A53%3A10+GMT', 'sid_tt': '992489de3c7a0ccac47c0a62640a1393', 'sid_ucp_v1': '1.0.0-KDFlYTFhM2VhZTUxYjU0NzUwYTZkNTI2NzdkMGM3MDI5M2M3NzBjMWEKGgiFiLj00KOx7GQQmIrowgYYsws4BEDqB0gEEAMaAm15IiA5OTI0ODlkZTNjN2EwY2NhYzQ3YzBhNjI2NDBhMTM5Mw', 'ssid_ucp_v1': '1.0.0-KDFlYTFhM2VhZTUxYjU0NzUwYTZkNTI2NzdkMGM3MDI5M2M3NzBjMWEKGgiFiLj00KOx7GQQmIrowgYYsws4BEDqB0gEEAMaAm15IiA5OTI0ODlkZTNjN2EwY2NhYzQ3YzBhNjI2NDBhMTM5Mw', 'store-country-code': 'sg', 'store-country-code-src': 'uid', 'store-country-sign': 'MEIEDMX4JVXBaSJLoYn9RwQga3xgMhS5obBFCnqMqkBYJFMXgK_GmcQ8_y9_J9twoZMEECCklb1TDqbY3CwAsCb6AEI', 'store-idc': 'alisg', 'tiktok_webapp_lang': 'en', 'tiktok_webapp_theme': 'light', 'tiktok_webapp_theme_source': 'auto', 'tt-target-idc': 'alisg', 'tt-target-idc-sign': 'hNKMuv5OxOTpoGZq8XmlquO08veYaNHmhE9Lr3TNVPBxkXE3vW-RU066F2oreF5CYhpCq0VJknokb6D3sBnIIsP0zD2TmysGzgHAaO8FMn8fQxVWErzsmFlE-_qnakmOV3AYTnRMhob9rH6Ho9tSLzJN3bsIgApnfDn6O8cuGrsIgu_5I33YaMZqxEFZl4FMB_C3XexKEfYwKi4OPHndfMCAKJk4VtYcOyumGHcZvaAyQpo2jiOtUzyARxTAiwmEqlXivb3Jag8v60Em-N3-RJ6coKOT-_GR9jotpEzDa5kuiQv87Jjk9bm8ikshm5Yn7W6IX2KmggTMQoDBOOrUW0_Cm0LLFJ-I10Erov_qpni6bItBWRLhf0cgyRElx0Db9DSVYXiOV1_ZNQvoUO07Vft3qBOr5ba1ka_lhWYAkdIScs6wQZy32RaeTYBPS-GFWM8K9xTWjR_bAMY5W7IJqKEnQrHBrrhprZ81ssMqxWSs0P9bzZcA6TMV7GN5wPcg', 'tt_chain_token': '2GZd9VD8sWSyFc8kdCXtFA==', 'tt_csrf_token': 'KRVCAJ4R-BWT2PsGtOpwn3VkjR3HZ9fxmimw', 'ttwid': '1%7CgSu5bFeW1wYzN91HlTRp9Omr2FZQN3lhOjZpYMwJd7M%7C1750730008%7C7068651bf0e2393d8f526a24a1791e1de93fac15d5b38f3038b941f0437e454f', 'uid_tt': '18115fbfaaa9aa14c2c66c9604fdc685b7c09af5b4348cbef78461adcac563b8', 'uid_tt_ss': '18115fbfaaa9aa14c2c66c9604fdc685b7c09af5b4348cbef78461adcac563b8'}, 'message_text': '向往', 'object': 'oxmoxmoxmoxm', 'receiver_username': 'bestgyu777,', 'small_task_id': 5456, 'task_type': 'private_message', 'user_app': False}, {'cookie_dict': {'_fbp': 'fb.1.1741571212518.1648668569', '_ga': 'GA1.1.1992765033.1740710270', '_ga_LWWPCY99PB': 'GS1.1.1746768732.20.1.1746768948.0.0.1317304137', 'cmpl_token': 'AgQQAPO8F-RO0rTmK7qGvN08_cjwx8Tbv5M3YN6jJQ', 'cookie-consent': '{%22optional%22:true%2C%22ga%22:true%2C%22af%22:true%2C%22fbp%22:true%2C%22lip%22:true%2C%22bing%22:true%2C%22ttads%22:true%2C%22reddit%22:true%2C%22hubspot%22:true%2C%22version%22:%22v10%22}', 'd_ticket': '1d1d03ebcded9d736f486165e65cf9fe77a59', 'delay_guest_mode_vid': '5', 'last_login_method': 'handle', 'living_user_id': '************', 'msToken': 'IFF8HufmT5FYLYlJgT_fRXhZ-vv1ZVj69og46STQY74mJp3zRSBXeHP4sycaE3FfVIfz6ifXOJot5L_8xoO4r10QL0bA_kNUWXuo7yu9p1aVht6JYNF6zMxDUoBMNYYzkdXjZfYg9pOtYB4=', 'multi_sids': '7479439474575066142%3A3ecf9e095e2871147a39adbc5bbef02b%7C7266774684409725958%**********************************%7C7266774587765376006%3Ae4b13e4cb171b58c301316890a508bff%7C7266774727351206917%3A992489de3c7a0ccac47c0a62640a1393', 'odin_tt': '5f622c5619101388e36e439aa17ff573e3eb6b70d3eb7c8cb0c926543ef865ec85e8ccfab1b284b83ba7fb848de836911a44fd40246e0502d8d71dbd918b2f777111f2bbd5286f630b8df6826bbc9212', 'passport_csrf_token': 'ce0f1134c49044fa503b49df1c463dbf', 'passport_csrf_token_default': 'ce0f1134c49044fa503b49df1c463dbf', 'passport_fe_beating_status': 'true', 'perf_feed_cache': '{%22expireTimestamp%22:1750845600000%2C%22itemIds%22:[%227507828074591816990%22%2C%227506102228789349654%22%2C%227486751505681255688%22]}', 's_v_web_id': 'verify_mc9vbrqd_PeEtYeqZ_8uCu_4AgO_BgtN_3UtyEF2tDtKy', 'sessionid': '992489de3c7a0ccac47c0a62640a1393', 'sessionid_ss': '992489de3c7a0ccac47c0a62640a1393', 'sid_guard': '992489de3c7a0ccac47c0a62640a1393%7C1750730008%7C15551982%7CSun%2C+21-Dec-2025+01%3A53%3A10+GMT', 'sid_tt': '992489de3c7a0ccac47c0a62640a1393', 'sid_ucp_v1': '1.0.0-KDFlYTFhM2VhZTUxYjU0NzUwYTZkNTI2NzdkMGM3MDI5M2M3NzBjMWEKGgiFiLj00KOx7GQQmIrowgYYsws4BEDqB0gEEAMaAm15IiA5OTI0ODlkZTNjN2EwY2NhYzQ3YzBhNjI2NDBhMTM5Mw', 'ssid_ucp_v1': '1.0.0-KDFlYTFhM2VhZTUxYjU0NzUwYTZkNTI2NzdkMGM3MDI5M2M3NzBjMWEKGgiFiLj00KOx7GQQmIrowgYYsws4BEDqB0gEEAMaAm15IiA5OTI0ODlkZTNjN2EwY2NhYzQ3YzBhNjI2NDBhMTM5Mw', 'store-country-code': 'sg', 'store-country-code-src': 'uid', 'store-country-sign': 'MEIEDMX4JVXBaSJLoYn9RwQga3xgMhS5obBFCnqMqkBYJFMXgK_GmcQ8_y9_J9twoZMEECCklb1TDqbY3CwAsCb6AEI', 'store-idc': 'alisg', 'tiktok_webapp_lang': 'en', 'tiktok_webapp_theme': 'light', 'tiktok_webapp_theme_source': 'auto', 'tt-target-idc': 'alisg', 'tt-target-idc-sign': 'hNKMuv5OxOTpoGZq8XmlquO08veYaNHmhE9Lr3TNVPBxkXE3vW-RU066F2oreF5CYhpCq0VJknokb6D3sBnIIsP0zD2TmysGzgHAaO8FMn8fQxVWErzsmFlE-_qnakmOV3AYTnRMhob9rH6Ho9tSLzJN3bsIgApnfDn6O8cuGrsIgu_5I33YaMZqxEFZl4FMB_C3XexKEfYwKi4OPHndfMCAKJk4VtYcOyumGHcZvaAyQpo2jiOtUzyARxTAiwmEqlXivb3Jag8v60Em-N3-RJ6coKOT-_GR9jotpEzDa5kuiQv87Jjk9bm8ikshm5Yn7W6IX2KmggTMQoDBOOrUW0_Cm0LLFJ-I10Erov_qpni6bItBWRLhf0cgyRElx0Db9DSVYXiOV1_ZNQvoUO07Vft3qBOr5ba1ka_lhWYAkdIScs6wQZy32RaeTYBPS-GFWM8K9xTWjR_bAMY5W7IJqKEnQrHBrrhprZ81ssMqxWSs0P9bzZcA6TMV7GN5wPcg', 'tt_chain_token': '2GZd9VD8sWSyFc8kdCXtFA==', 'tt_csrf_token': 'KRVCAJ4R-BWT2PsGtOpwn3VkjR3HZ9fxmimw', 'ttwid': '1%7CgSu5bFeW1wYzN91HlTRp9Omr2FZQN3lhOjZpYMwJd7M%7C1750730008%7C7068651bf0e2393d8f526a24a1791e1de93fac15d5b38f3038b941f0437e454f', 'uid_tt': '18115fbfaaa9aa14c2c66c9604fdc685b7c09af5b4348cbef78461adcac563b8', 'uid_tt_ss': '18115fbfaaa9aa14c2c66c9604fdc685b7c09af5b4348cbef78461adcac563b8'}, 'message_text': '向往', 'object': 'oxmoxmoxmoxm', 'receiver_username': 'roddri_i', 'small_task_id': 5457, 'task_type': 'private_message', 'user_app': False}], 'platform': 0, 'task_id': 1434, 'task_type': 'private_message'}, 'msg': '成功', 're_code': 0, 'success': True}
                        self.test_botton = 0
                    else:
                        response_json = {'data': [], 'msg': '成功', 're_code': 0, 'success': True}
                    """
                    self.logger.debug(f'获取{task_type.center(25, " ")}任务接口响应：{response_json}')
                    all_task_data: Union[dict, None] = response_json.get("data", None)
                    if all_task_data and all_task_data.get("details"):
                        self.task_queues_dict[task_type].put(all_task_data)
                        continue
                else:
                    self.logger.warning(f"获取 {task_type} 任务接口响应成功，但无内容，状态码：{response.status_code}")
                time.sleep(30)
            except Exception as e:
                self.logger.error(f"获取任务接口出错：{e}")
                time.sleep(10)

    def _send_callback_data(self, logger: "logging.Logger", data, context=None):
        """发送回调数据到指定URL"""
        context = context or {}
        try:
            task_type = data.get("task_type")
            task_status = data.get("task_status")
            task_id = data.get("task_id")
            # 优先从context获取详细信息，保证日志最全
            small_task_id = context.get('small_task_id')
            object_id = context.get('object_id')

            log_parts = [f"类型: {task_type}", f"ID: {task_id}"]
            if small_task_id:
                log_parts.append(f"子ID: {small_task_id}")
            if object_id:
                log_parts.append(f"对象: {object_id}")
            log_parts.append(f"状态: {task_status}")

            # 强制使用主日志记录器来记录所有回调
            self.logger.debug(f"[回调] {' | '.join(log_parts)}")
            logger.debug(f'任务回调更新的 data：{data}')

            json_data = json.dumps(data, separators=(",", ":"))
            response: "Response" = self.do_req(
                method="POST",
                url=self.upload_url,
                headers=self.headers,
                data=json_data,
                no_proxy=True
            )
            response_json = response.json()
            if not response_json.get("success", False):
                logger.error(f"[回调失败] {response_json.get('msg', '未知错误')}")
        except Exception as e:
            self.logger.error(f"[回调异常] {e}", exc_info=True)

    def _handle_paginated_end_signal(self, logger, consumer_result_dict, context):
        """处理分页任务的结束信号"""
        final_status = consumer_result_dict.get("task_status")
        if final_status is None:
            final_status = 2 if consumer_result_dict.get("status") == 1 else 3

        data_payload = {}

        if consumer_result_dict.get("not_exist", False):
            final_status = 2
            data_payload["message"] = "目标不存在或内容私密"
            data_payload["not_exist"] = True
            data_payload["status"] = 2
        elif final_status == 3:
            error_data = consumer_result_dict.get("data", {})
            data_payload["error"] = error_data.get("text", "任务处理失败")
            data_payload["status"] = 0
            if "cursor" in error_data:
                data_payload["cursor"] = error_data["cursor"]
        else:
            data_payload["message"] = "任务最终处理完成"
            data_payload["status"] = 2

        if context.get("small_task_id"):
            data_payload["small_task_id"] = context["small_task_id"]

        upload_data = {
            "code": 200, "task_id": context["task_id"], "task_type": context["task_type"],
            "data": data_payload, "task_status": final_status,
        }
        self._send_callback_data(logger, upload_data, context=context)

    def _handle_paginated_data_batch(self, logger, result_list, context):
        """处理正常的数据批次回调"""
        task_type = context["task_type"]
        LIST_KEY_MAP = {
            "blogger_fans_collect": "userList",
            "blogger_follow_collect": "userList",
            "video_id_collect": "itemList",
            "comment_collect": "commentList",
            "comment_reply_collect": "commentList",
        }
        list_key = LIST_KEY_MAP.get(task_type, "itemList")
        data_payload = {list_key: result_list}

        if context.get("small_task_id"):
            data_payload["small_task_id"] = context["small_task_id"]

        upload_data = {
            "code": 200, "task_id": context["task_id"], "task_type": task_type,
            "data": data_payload, "task_status": 0,  # 分页任务进行中
        }
        self._send_callback_data(logger, upload_data, context=context)

    # 分批回调类任务的回调
    def __upload_batch_task(self):
        """识别并分发不同类型的分页回调任务。"""
        while True:
            while not self.upload_batch_queue.empty():
                consumer_result_dict, task_type = self.upload_batch_queue.get()
                # 强制使用主日志记录器
                logger = self.logger

                context = {
                    "small_task_id": consumer_result_dict.get("small_task_id"),
                    "object_id": consumer_result_dict.get("object_id"),
                    "task_id": consumer_result_dict.get("task_id", 0),
                    "task_type": task_type,
                }

                result_list = consumer_result_dict.get("result_list", [])

                if not result_list:
                    # 这是结束信号
                    self._handle_paginated_end_signal(logger, consumer_result_dict, context)
                else:
                    # 这是数据批次
                    self._handle_paginated_data_batch(logger, result_list, context)
            time.sleep(1)

    # 立即完成类任务的回调
    def __upload_task(self):
        """处理即时完成任务的回调，将多个子任务结果合并后一次性回调。"""
        while True:
            while not self.upload_queue.empty():
                consumer_result_dict, task_type = self.upload_queue.get()
                logger = self.spider_logger_dict.get(task_type) or self.logger

                result_list = consumer_result_dict.get("result_list", [])
                task_id = consumer_result_dict.get("task_id", 0)

                if not result_list:
                    logger.warning(f"任务 {task_id} 在 upload_queue 中收到空结果列表, 跳过")
                    continue

                num_results = len(result_list)
                # 区分类型状态码处理
                if task_type in ["blogger_collect", "video_collect"]:
                    # 采集类任务直接使用 2/3
                    task_status = 2 if all(r.get("status") == 2 for r in result_list) else 3
                else:
                    # 其他任务保持原有的 0/1 -> 2/3 转换
                    task_status = 2 if all(r.get("status") == 1 for r in result_list) else 3

                log_context = {
                    "task_id": task_id,
                    "task_type": consumer_result_dict.get("task_type", ""),
                    "small_task_id": result_list[0].get("small_task_id") if result_list else None
                }
                logger.info(f"任务 {task_id} | 合并回调 | 子任务数: {num_results} | 最终状态: {task_status}")

                upload_payload = {
                    "code": 200,
                    "task_id": task_id,
                    "data": result_list,  # 关键：将整个列表作为 data 字段的值
                    "task_status": task_status,
                    "task_type": log_context["task_type"],
                }
                self._send_callback_data(logger, upload_payload, context=log_context)
            time.sleep(10)

    def add_task_threads(self, name: str, target: Callable, args=()):
        t = threading.Thread(target=target, args=args, daemon=True, name=name)
        self.download_threads.append((t, name, target))
        t.start()

    def monitor_task_threads(self):
        for i, (t, name, target) in enumerate(self.download_threads):
            if not t.is_alive():
                self.logger.warning(f"下载任务线程 {name} 休眠，重启中")
                new_t = threading.Thread(target=target, daemon=True, name=name)
                new_t.start()
                self.download_threads[i] = (new_t, name, target)

    def monitor_process(self):
        for pid, p in self.processes.items():
            p: multiprocessing.Process
            if not p.is_alive():
                task_type = self.pid_to_tasktype.pop(pid, None)
                if task_type:
                    self.restart_process(pid, task_type)
        while not self.heartbeat_queue.empty():
            heartbeat_data = self.heartbeat_queue.get()
            self.heartbeat_dict.update(heartbeat_data)

        now_time = time.time()
        # 每5分钟记录一次心跳状态
        if now_time - self._last_heartbeat_log >= 300:
            self.logger.debug(f"[心跳] 活跃进程: {len(self.heartbeat_dict)}")
            self._last_heartbeat_log = now_time

        heartbeat_dict = self.heartbeat_dict.copy()
        for pid in heartbeat_dict:
            if now_time - heartbeat_dict[pid] >= self.restart_time:
                task_type = self.pid_to_tasktype.pop(pid, None)
                if task_type:
                    self.restart_process(pid, task_type)

    def stop_process(self, pid):
        if pid in self.processes:
            try:
                self.processes[pid].terminate()
                self.processes[pid].join(timeout=3)
            except Exception as e:
                self.logger.warning(f"终止进程 {pid} 失败: {e}")
            finally:
                self.processes.pop(pid, None)
                self.heartbeat_dict.pop(pid, None)

    def restart_process(self, pid, task_type):
        self.logger.warning(f"进程 {pid} 退出，重启中")
        self.stop_process(pid)
        p = multiprocessing.Process(
            target=run_mid_manager,
            kwargs={
                "main_path": self.main_path,
                "init_info": self.init_info,
                "task_queue": self.task_queues_dict[task_type],
                "upload_queue": self.upload_queue,
                "upload_batch_queue": self.upload_batch_queue,
                "heartbeat_queue": self.heartbeat_queue,
                "log_queue": self.log_queue,
                "spider_log_queue_dict": self.spider_log_queue_dict,
                "shared_namespace": self.shared_namespace  # 传递共享对象
            },
            daemon=True
        )
        p.start()
        self.processes[p.pid] = p
        self.pid_to_tasktype[p.pid] = task_type
        self.heartbeat_dict[p.pid] = time.time()

    def main(self, log_name=""):
        with multiprocessing.Manager() as mp_manager:
            # 1. 初始化多进程管理器和共享命名空间
            self.shared_namespace = mp_manager.Namespace()
            self.shared_namespace.default_ms_token = self.init_info["default_ms_token"]

            self.heartbeat_queue = multiprocessing.Queue()
            self.upload_queue = multiprocessing.Queue(maxsize=5000)
            self.upload_batch_queue = multiprocessing.Queue(maxsize=5000)
            self.log_queue = multiprocessing.Queue(maxsize=5000)

            self.logger = self.init_logger(name="mananger", log_level=self.log_level, queue=self.log_queue)
            self.log_listener_list.append(self.start_log_listener(log_queue=self.log_queue, log_name=log_name))
            process_num = sum(self.init_info["task_process_num"].values())
            single_process_thread_num = self.init_info.get("single_process_num", 100)

            # 简化启动日志，使用更清晰的格式
            self.logger.info(
                f'\n[系统初始化]\n' +
                f'任务类型: {list(self.init_info["task_process_num"].keys())}\n' +
                f'进程数: {process_num} | 线程数: {process_num * single_process_thread_num}\n' +
                f'进程超时: {self.restart_time}秒 | 请求超时: {self.init_info["max_req_timeout"]}秒'
            )

            # 2. 启动后台定时更新 token 的线程
            token_thread = threading.Thread(target=self._token_updater_thread, daemon=True)
            token_thread.start()
            self.logger.info("Token 定时更新线程已启动，将每24小时执行一次。")

            for task_type in PAGINATED_COLLECT_TASKS:
                spider_log_queue = multiprocessing.Queue(maxsize=5000)
                spider_logger = self.init_logger(name=task_type, log_level=self.log_level, queue=spider_log_queue)
                self.log_listener_list.append(
                    self.start_log_listener(log_queue=spider_log_queue, log_name=task_type, spider_log=True))
                self.spider_logger_dict[task_type] = spider_logger
                self.spider_log_queue_dict[task_type] = spider_log_queue

            self.task_queues_dict = {t: multiprocessing.Queue(maxsize=100) for t in
                                     self.init_info["task_process_num"]}  # 每种任务类型自己的队列

            # 获取任务（每种任务1条线程）
            for task_type in self.init_info["task_process_num"]:
                self.add_task_threads(name=task_type, target=self.__download_task, args=(task_type,))

            # 回写任务（1条线程）
            threading.Thread(target=self.__upload_task, daemon=True).start()
            threading.Thread(target=self.__upload_batch_task, daemon=True).start()

            for task_type in self.init_info["task_process_num"]:
                p_num = self.init_info["task_process_num"][task_type]
                for i in range(p_num):
                    p = multiprocessing.Process(
                        target=run_mid_manager,
                        kwargs={
                            "main_path": self.main_path,
                            "init_info": self.init_info,
                            "task_queue": self.task_queues_dict[task_type],
                            "upload_queue": self.upload_queue,
                            "upload_batch_queue": self.upload_batch_queue,
                            "heartbeat_queue": self.heartbeat_queue,
                            "log_queue": self.log_queue,
                            "spider_log_queue_dict": self.spider_log_queue_dict,
                            "shared_namespace": self.shared_namespace  # 传递共享对象
                        },
                        daemon=True
                    )
                    p.start()
                    self.processes[p.pid] = p
                    self.pid_to_tasktype[p.pid] = task_type
                    self.heartbeat_dict[p.pid] = time.time()

            try:
                while True:
                    self.monitor_task_threads()
                    self.monitor_process()
                    time.sleep(60)
            except KeyboardInterrupt as e:
                self.logger.debug("检测到按键退出，程序退出...")
            finally:
                for listener in self.log_listener_list:
                    listener.stop()


if __name__ == "__main__":
    main_path = Path(sys.argv[0]).resolve().parent
    Manager().main(log_name="tk_project")
